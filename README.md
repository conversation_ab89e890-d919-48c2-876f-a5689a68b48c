# ROBO-RESEARCHER-2000 🤖

## Sistema de Investigación UX Semi-Automatizado

Un sistema completo que automatiza el proceso de investigación de User Experience (UX) desde transcripciones hasta presentaciones finales, utilizando n8n como orquestador central y herramientas open-source.

### 🎯 Objetivo

Digitalizar y unificar el proceso de investigación UX, reduciendo reuniones sincrónicas y aumentando la eficiencia mediante automatización inteligente.

### 🏗️ Arquitectura

```
Cliente Web (GitHub Pages) → n8n Workflow → MinIO Storage → Wiki.js Documentation
                                ↓
                        Python Scripts (NLTK/spaCy) → OpenRouter AI → Marp Presentations
```

### 🛠️ Componentes

- **Cliente Web**: Interfaz para subir transcripciones y configurar parámetros
- **n8n**: Orquestador de 17 pasos automatizados
- **MinIO**: Almacenamiento S3-compatible self-hosted
- **Wiki.js**: Repositorio central de documentación
- **Python Scripts**: Análisis cualitativo con NLTK/spaCy
- **OpenRouter AI**: Asistencia de IA para codificación e insights
- **Marp**: Generación automática de presentaciones

### 📁 Estructura del Proyecto

```
robo-researcher-2000/
├── client/                 # Cliente web (GitHub Pages)
│   ├── index.html
│   ├── css/
│   ├── js/
│   └── components/
├── workflows/              # Workflows de n8n
│   ├── main-workflow.json
│   └── templates/
├── scripts/                # Scripts Python para análisis
│   ├── text_preprocessing.py
│   ├── coding_engine.py
│   └── modules/
├── infrastructure/         # Configuración Docker
│   ├── docker-compose.yml
│   ├── minio/
│   ├── wikijs/
│   └── n8n/
└── docs/                   # Documentación
    ├── setup.md
    ├── user-guide.md
    └── api-reference.md
```

### 🚀 Proceso Automatizado (17 Pasos)

1. **Webhook Trigger** - Recibe datos del cliente
2. **Validate Input** - Valida formato y contenido
3. **Upload to MinIO** - Almacena transcripción
4. **Text Preprocessing** - Limpieza y anonimización
5. **Segmentation** - División por tópicos
6. **Deductive Coding** - Códigos predefinidos
7. **Open Coding AI** - IA sugiere códigos emergentes
8. **Category Grouping** - Agrupa códigos similares
9. **Affinity Mapping** - Genera visualizaciones
10. **Quantitative Analysis** - Métricas y frecuencias
11. **Pattern Detection** - IA detecta patrones
12. **Insight Generation** - Genera insights estructurados
13. **Archetype Creation** - Crea arquetipos de usuarios
14. **HMW Generation** - "How Might We" questions
15. **Opportunity Prioritization** - Matriz RICE
16. **Presentation Generation** - Marp → PPTX automático
17. **Documentation & Email** - Wiki.js + notificaciones

### 🔧 Requisitos Técnicos

**VPS/Servidor:**
- 4 CPU cores, 6GB RAM, 60GB storage
- Docker y Docker Compose
- Python 3.9+ con bibliotecas científicas
- Node.js para Marp CLI

**APIs y Servicios:**
- OpenRouter API para IA
- SMTP server para notificaciones
- GitHub Pages para cliente web

### 📋 Instalación Rápida

1. **Clonar repositorio:**
```bash
git clone https://github.com/tu-usuario/robo-researcher-2000.git
cd robo-researcher-2000
```

2. **Configurar infraestructura:**
```bash
cd infrastructure
docker-compose up -d
```

3. **Configurar variables de entorno:**
```bash
cp .env.example .env
# Editar .env con tus APIs y configuraciones
```

4. **Importar workflow n8n:**
- Acceder a n8n en `http://tu-servidor:5678`
- Importar `workflows/main-workflow.json`

5. **Desplegar cliente web:**
- Habilitar GitHub Pages en el repositorio
- El cliente estará disponible en `https://tu-usuario.github.io/robo-researcher-2000`

### 🎨 Uso

1. **Subir transcripción**: Usar el cliente web para subir archivo .txt
2. **Configurar parámetros**: Email, APIs, metadatos del proyecto
3. **Ejecutar análisis**: El workflow se ejecuta automáticamente
4. **Recibir resultados**: Presentación PPTX, documentación Wiki.js, email

### 🤝 Contribuir

1. Fork el repositorio
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

### 📄 Licencia

MIT License - ver [LICENSE](LICENSE) para detalles.

### 🆘 Soporte

- **Issues**: [GitHub Issues](https://github.com/tu-usuario/robo-researcher-2000/issues)
- **Documentación**: [docs/](docs/)
- **Email**: <EMAIL>

---

**Desarrollado con ❤️ para automatizar la investigación UX**
