# ROBO-RESEARCHER-2000 🤖

**Automated UX Research Analysis System**

Transform user interview transcriptions into actionable insights through a 17-step automated workflow powered by AI, natural language processing, and modern web technologies.

## 🌟 Features

- **Automated Analysis Pipeline**: 17-step workflow from raw transcription to final presentation
- **AI-Powered Insights**: Uses OpenRouter API with Claude-3-Sonnet for intelligent analysis
- **Multiple Output Formats**: Generates presentations (PPTX/PDF), documentation (Wiki.js), and visualizations (SVG)
- **Web-Based Interface**: Clean, responsive client application for easy interaction
- **Self-Hosted Infrastructure**: Complete Docker-based deployment with MinIO, n8n, and Wiki.js
- **Comprehensive Testing**: Full test suite with integration, performance, and deployment validation

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Client Web    │───▶│  n8n Webhook │───▶│  Processing     │
│   Application   │    │   Trigger    │    │   Pipeline      │
└─────────────────┘    └──────────────┘    └─────────────────┘
                                                     │
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Email         │◀───│    MinIO     │◀───│  AI Analysis    │
│ Notification    │    │   Storage    │    │  (OpenRouter)   │
└─────────────────┘    └──────────────┘    └─────────────────┘
                                │
                       ┌──────────────┐
                       │   Wiki.js    │
                       │ Documentation│
                       └──────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Docker & Docker Compose
- Python 3.8+
- OpenRouter API key
- SMTP credentials (for email notifications)

### 1. Clone and Setup

```bash
git clone https://github.com/your-username/robo-researcher-2000.git
cd robo-researcher-2000

# Start infrastructure
cd infrastructure
chmod +x setup.sh
./setup.sh
```

### 2. Configure Environment

Create `.env` file:

```bash
OPENROUTER_API_KEY=your_openrouter_api_key
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
```

### 3. Import Workflows

1. Open n8n at `http://localhost:5678`
2. Import `workflows/test-workflow-simple.json`
3. Import `workflows/main-workflow-complete.json`
4. Configure credentials (OpenRouter, SMTP, MinIO)

### 4. Test the System

```bash
# Run validation tests
python tests/run_tests.py --mode quick

# Open client application
cd client
python -m http.server 8080
# Visit http://localhost:8080
```

## 📋 17-Step Analysis Pipeline

1. **Webhook Trigger** - Receives data from client
2. **Input Validation** - Validates required fields and data quality
3. **Upload to MinIO** - Stores original transcription
4. **Text Preprocessing** - Cleans and normalizes text
5. **Segmentation** - Divides text by topics and speakers
6. **Deductive Coding** - Applies predefined codes
7. **Open Coding AI** - AI suggests emergent codes
8. **Category Grouping** - Groups codes into categories
9. **Affinity Mapping** - Creates visual groupings
10. **Quantitative Analysis** - Statistical analysis of coded data
11. **Pattern Detection** - AI identifies behavioral patterns
12. **Insight Generation** - Generates structured insights
13. **Archetype Creation** - Creates user personas
14. **HMW Generation** - Generates "How Might We" questions
15. **Opportunity Prioritization** - RICE methodology prioritization
16. **Presentation Generation** - Creates PPTX/PDF presentations
17. **Documentation & Email** - Wiki.js docs and email delivery

## 🛠️ Technology Stack

### Core Infrastructure
- **n8n**: Workflow automation and orchestration
- **MinIO**: S3-compatible object storage
- **Wiki.js**: Documentation and knowledge management
- **PostgreSQL**: Database for n8n and Wiki.js

### Analysis & AI
- **OpenRouter API**: AI analysis with Claude-3-Sonnet
- **Python**: Text processing and analysis
- **NLTK/spaCy**: Natural language processing
- **scikit-learn**: Machine learning and clustering

### Presentation & Visualization
- **Marp**: Markdown to presentation conversion
- **Excalidraw**: Affinity mapping visualizations
- **Matplotlib/Seaborn**: Statistical charts

### Client Interface
- **HTML/CSS/JavaScript**: Responsive web application
- **Fetch API**: Communication with n8n webhooks

## 📊 Sample Output

The system generates:

- **Executive Presentation**: 15-20 slide PPTX with key findings
- **Detailed Documentation**: Structured Wiki.js pages with full analysis
- **Affinity Maps**: SVG visualizations of code relationships
- **Statistical Reports**: Quantitative analysis with charts
- **User Archetypes**: Detailed personas with quotes and pain points
- **Prioritized Opportunities**: RICE-scored improvement recommendations

## 🧪 Testing

### Run All Tests
```bash
python tests/run_tests.py --mode all
```

### Test Individual Components
```bash
# Deployment validation
python tests/deployment_validator.py

# Integration testing
python tests/integration_test.py

# Client application testing
open tests/client_test.html
```

### Performance Benchmarks
- **Simple workflow**: < 5 seconds
- **Complete analysis**: 15-20 minutes
- **File processing**: Up to 10MB transcriptions
- **Concurrent users**: 5-10 simultaneous analyses

## 📚 Documentation

- **[Setup Guide](docs/setup-guide.md)**: Detailed installation instructions
- **[Integration Guide](docs/integration-guide.md)**: Component integration details
- **[Workflow Documentation](workflows/workflow-documentation.md)**: Complete workflow reference
- **[API Reference](docs/api-reference.md)**: Client-server communication
- **[User Manual](docs/user-manual.md)**: End-user instructions

## 🔧 Configuration

### Environment Variables
```bash
# Required
OPENROUTER_API_KEY=sk-or-...
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Optional
N8N_URL=http://localhost:5678
MINIO_URL=http://localhost:9000
WIKIJS_URL=http://localhost:3000
```

### Service Ports
- **n8n**: 5678
- **MinIO**: 9000 (API), 9001 (Console)
- **Wiki.js**: 3000
- **PostgreSQL**: 5432
- **Client**: 8080 (development)

## 🚀 Deployment

### Development
```bash
# Local development with Docker
docker-compose up -d
```

### Production
```bash
# Production deployment
docker-compose -f docker-compose.prod.yml up -d

# With SSL/HTTPS
docker-compose -f docker-compose.prod.yml -f docker-compose.ssl.yml up -d
```

### Cloud Deployment
- **AWS**: Use ECS with RDS and S3
- **Google Cloud**: Use Cloud Run with Cloud SQL and Cloud Storage
- **Azure**: Use Container Instances with Azure Database and Blob Storage

## 🔒 Security

- **API Keys**: Stored securely in n8n credentials
- **Data Privacy**: Automatic anonymization of personal information
- **Network Security**: Internal Docker network isolation
- **HTTPS**: SSL/TLS encryption for production
- **Backup**: Automated backup procedures for data protection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests before committing
python tests/run_tests.py --mode all

# Format code
black scripts/ tests/
```

## 📈 Roadmap

### Version 2.0
- [ ] Multi-language support (Spanish, French, German)
- [ ] Advanced AI models (GPT-4, Gemini Pro)
- [ ] Real-time collaboration features
- [ ] Mobile application
- [ ] Advanced analytics dashboard

### Version 2.1
- [ ] Video transcription integration
- [ ] Automated survey analysis
- [ ] Integration with design tools (Figma, Sketch)
- [ ] Advanced visualization options

## 🐛 Troubleshooting

### Common Issues

**Services not starting**
```bash
docker-compose down
docker-compose up -d
docker ps  # Check container status
```

**Webhook not responding**
```bash
curl http://localhost:5678/healthz
# Check n8n logs: docker logs robo-researcher-n8n
```

**AI API failures**
- Verify OpenRouter API key
- Check API quota and rate limits
- Review n8n execution logs

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **n8n**: Powerful workflow automation platform
- **OpenRouter**: AI API aggregation service
- **MinIO**: High-performance object storage
- **Wiki.js**: Modern wiki software
- **NLTK/spaCy**: Natural language processing libraries

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-username/robo-researcher-2000/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/robo-researcher-2000/discussions)
- **Email**: <EMAIL>

---

**Made with ❤️ for UX Researchers by UX Researchers**

Transform your qualitative research workflow with the power of automation and AI.
