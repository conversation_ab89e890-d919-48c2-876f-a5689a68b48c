# Core Python libraries for text analysis
nltk==3.8.1
spacy==3.7.2
pandas==2.1.4
numpy==1.24.3
scikit-learn==1.3.2

# NLP models and language processing
textblob==0.17.1
wordcloud==1.9.2

# Data visualization
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Web requests and APIs
requests==2.31.0
urllib3==2.1.0

# File handling and utilities
openpyxl==3.1.2
python-docx==1.1.0
PyPDF2==3.0.1

# MinIO S3 client
minio==7.2.0

# Email and SMTP (smtplib is part of Python standard library)
# No additional package needed for basic SMTP functionality

# JSON and data processing
jsonschema==4.20.0
pyyaml==6.0.1

# Date and time utilities
python-dateutil==2.8.2
pytz==2023.3

# Environment variables
python-dotenv==1.0.0

# Logging and monitoring
loguru==0.7.2

# Testing (optional)
pytest==7.4.3
pytest-cov==4.1.0

# Development tools
black==23.11.0
flake8==6.1.0
isort==5.12.0

# spaCy language models (install separately)
# python -m spacy download en_core_web_sm
# python -m spacy download es_core_news_sm
