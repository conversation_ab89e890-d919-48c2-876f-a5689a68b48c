# ROBO-RESEARCHER-2000 Deployment Plan for nuc2

## 🎯 **RECOMMENDED DEPLOYMENT TARGET: nuc2**

### ✅ **nuc2 System Assessment - EXCELLENT CANDIDATE**

#### Resource Analysis
- **CPU**: 4 cores (2 physical, 4 threads) - **1.38% utilization**
- **Memory**: 8.2GB total, **4.4GB available** (only 2.1GB used - 26% usage)
- **Storage**: 39GB total, **33GB available**
- **Load Average**: 0.20, 0.18, 0.11 (very low)
- **Status**: Online, stable (2w 2d 8h uptime)

#### Current Services
- **Container 102**: 2 CPUs, 9.7GB RAM allocated
- **Container 104**: 1 CPU, 3.8GB RAM allocated
- **Actual Memory Usage**: Only 2.1GB (plenty of headroom)

#### ✅ **DEPLOYMENT FEASIBILITY: EXCELLENT**
- **Available Memory**: 4.4GB (exceeds 2GB minimum requirement)
- **CPU Capacity**: Abundant (1.38% current usage)
- **Storage**: Sufficient (33GB available)
- **Network**: Stable cluster connectivity

## 🚀 **Deployment Strategy for nuc2**

### Phase 1: Pre-Deployment Preparation

#### 1.1 Access nuc2 via Proxmox
```bash
# From nuc1 (Proxmox management node)
pvesh create /nodes/nuc2/lxc/200 \
  --ostemplate local:vztmpl/ubuntu-22.04-standard_22.04-1_amd64.tar.zst \
  --hostname robo-researcher \
  --memory 4096 \
  --cores 2 \
  --rootfs local-lvm:20 \
  --net0 name=eth0,bridge=vmbr0,ip=dhcp \
  --password robo-researcher-2000

# Start the container
pct start 200

# Enter the container
pct enter 200
```

#### 1.2 Alternative: Direct Docker Installation on nuc2
```bash
# Connect to nuc2 host directly (if possible)
# Install Docker on nuc2 host system
ssh nuc2  # (if direct access available)
# OR use Proxmox console access
```

### Phase 2: System Setup

#### 2.1 Install Dependencies
```bash
# Update system
apt update && apt upgrade -y

# Install Docker and Docker Compose
apt install -y docker.io docker-compose git curl

# Enable Docker service
systemctl enable docker
systemctl start docker

# Verify Docker installation
docker --version
docker-compose --version
```

#### 2.2 Clone Repository
```bash
# Clone ROBO-RESEARCHER-2000
git clone https://github.com/c42705/robo-researcher-2000.git
cd robo-researcher-2000

# Verify all files are present
ls -la
```

### Phase 3: Configuration

#### 3.1 Environment Setup
```bash
# Create optimized environment for nuc2
cat > .env << 'EOF'
# Core Database Configuration
POSTGRES_DB=robo_researcher
POSTGRES_USER=robo_user
POSTGRES_PASSWORD=robo_pass_2000

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000
N8N_HOST=robo.stargety.com
N8N_PROTOCOL=https
N8N_SECURE_COOKIE=false

# MinIO Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin

# Wiki.js Configuration
WIKIJS_DB_PASSWORD=wikijs_pass_2000

# Email Configuration (Stargety SMTP)
SMTP_HOST=mail.stargety.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# API Configuration
OPENROUTER_API_KEY=your_openrouter_key

# Client Configuration
N8N_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/robo-researcher
N8N_TEST_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/test-robo-researcher
API_BASE_URL=http://localhost:5678
NODE_ENV=production
EOF
```

#### 3.2 Resource Optimization for nuc2
```bash
# Create resource-optimized docker-compose override
cat > docker-compose.override.yml << 'EOF'
version: '3.8'

services:
  robo-researcher-postgres:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  robo-researcher-n8n:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  robo-researcher-minio:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  robo-researcher-wikijs:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  robo-researcher-client:
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  robo-researcher-redis:
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
EOF
```

### Phase 4: Deployment

#### 4.1 Deploy Services
```bash
# Build and start all services
docker-compose -f infrastructure/docker-compose.yml up -d --build

# Monitor deployment
docker-compose -f infrastructure/docker-compose.yml logs -f

# Verify all containers are running
docker ps
```

#### 4.2 Health Checks
```bash
# Wait for services to start (2-3 minutes)
sleep 180

# Check service health
curl -f http://localhost:80/health          # Client
curl -f http://localhost:5678/healthz       # n8n
curl -f http://localhost:9000/minio/health/live  # MinIO
curl -f http://localhost:3000               # Wiki.js

# Check container resource usage
docker stats --no-stream
```

### Phase 5: Network Configuration

#### 5.1 Get nuc2 IP Address
```bash
# Find nuc2 IP address
ip addr show | grep inet
# Expected: 192.168.0.xxx
```

#### 5.2 Configure Reverse Proxy
```bash
# Update nginxproxymanager (Container 101 on nuc1)
# Add proxy hosts:
# - robo.stargety.com -> nuc2_ip:80 (Client)
# - api.robo.stargety.com -> nuc2_ip:5678 (n8n)
# - minio.robo.stargety.com -> nuc2_ip:9001 (MinIO)
# - wiki.robo.stargety.com -> nuc2_ip:3000 (Wiki.js)
```

#### 5.3 DNS Update
```bash
# Update DNS record
# robo.stargety.com -> nuc2_ip (currently points to *************)
```

### Phase 6: Testing and Validation

#### 6.1 Run Deployment Tests
```bash
# Run comprehensive test suite
python3 tests/final_comprehensive_test.py

# Run integration tests
python3 tests/integration_test.py

# Test client application
curl -X POST http://localhost:80/api/webhook/test-robo-researcher \
  -H "Content-Type: application/json" \
  -d '{"project_name":"Test","email":"<EMAIL>","transcription":"Test transcription"}'
```

#### 6.2 Performance Monitoring
```bash
# Monitor system resources during operation
watch -n 5 'free -h && echo "---" && docker stats --no-stream'

# Check logs for any issues
docker-compose -f infrastructure/docker-compose.yml logs --tail=50
```

## 📊 **Expected Resource Usage on nuc2**

### Projected Memory Usage
- **PostgreSQL**: 256-512MB
- **n8n**: 512MB-1GB
- **MinIO**: 256-512MB
- **Wiki.js**: 256-512MB
- **Client (Nginx)**: 128-256MB
- **Redis**: 128-256MB
- **System overhead**: 512MB
- **Total**: ~2-3GB (well within 4.4GB available)

### Projected CPU Usage
- **Normal operation**: 10-20% CPU usage
- **During analysis**: 30-50% CPU usage
- **Peak load**: 60-80% CPU usage
- **Headroom**: Excellent (current 1.38% usage)

## 🔧 **Maintenance and Monitoring**

### Daily Monitoring
```bash
# Check system health
docker ps
docker stats --no-stream
free -h
df -h

# Check logs for errors
docker-compose -f infrastructure/docker-compose.yml logs --tail=20 | grep -i error
```

### Weekly Maintenance
```bash
# Update containers
docker-compose -f infrastructure/docker-compose.yml pull
docker-compose -f infrastructure/docker-compose.yml up -d

# Clean up unused images
docker system prune -f

# Backup data
docker run --rm -v robo-researcher_postgres_data:/source -v $(pwd)/backups:/backup alpine tar czf /backup/postgres_$(date +%Y%m%d).tar.gz -C /source .
```

## 🎯 **Deployment Timeline**

- **Phase 1-2**: 30 minutes (Setup and installation)
- **Phase 3**: 15 minutes (Configuration)
- **Phase 4**: 20 minutes (Deployment and startup)
- **Phase 5**: 15 minutes (Network configuration)
- **Phase 6**: 30 minutes (Testing and validation)
- **Total**: ~2 hours for complete deployment

## ✅ **Success Criteria**

1. **All containers running** without errors
2. **Client accessible** at http://robo.stargety.com
3. **API endpoints responding** correctly
4. **Memory usage < 3.5GB** (leaving 1GB headroom)
5. **CPU usage < 50%** during normal operation
6. **All health checks passing**
7. **Test workflow completing** successfully

## 🚨 **Rollback Plan**

If deployment fails:
```bash
# Stop all containers
docker-compose -f infrastructure/docker-compose.yml down

# Remove volumes (if needed)
docker volume prune -f

# Restore system state
# (nuc2 will return to previous low-resource state)
```

---

## 📋 **Final Recommendation**

**✅ PROCEED WITH DEPLOYMENT ON nuc2**

nuc2 is an excellent candidate for ROBO-RESEARCHER-2000 deployment with:
- **Abundant resources** (4.4GB available memory)
- **Low current utilization** (1.38% CPU)
- **Stable system** (2+ weeks uptime)
- **Sufficient storage** (33GB available)

**Expected outcome**: Successful deployment with excellent performance and resource headroom for growth.

**Next step**: Execute deployment plan with confidence.
