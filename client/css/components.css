/* ROBO-RESEARCHER-2000 Components */

/* Progress Components */
.progress-container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  padding: 2rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
  width: 0%;
}

.progress-text {
  text-align: center;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2rem;
}

.progress-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--border-color);
  transition: var(--transition);
}

.progress-step.active {
  border-left-color: var(--primary-color);
  background: rgb(37 99 235 / 0.05);
}

.progress-step.completed {
  border-left-color: var(--success-color);
  background: rgb(16 185 129 / 0.05);
}

.progress-step.error {
  border-left-color: var(--error-color);
  background: rgb(239 68 68 / 0.05);
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  background: var(--border-color);
  color: var(--text-secondary);
  flex-shrink: 0;
}

.progress-step.active .step-icon {
  background: var(--primary-color);
  color: white;
}

.progress-step.completed .step-icon {
  background: var(--success-color);
  color: white;
}

.progress-step.error .step-icon {
  background: var(--error-color);
  color: white;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.step-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Results Components */
.results-container {
  max-width: 1000px;
  margin: 0 auto;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.result-card {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  padding: 1.5rem;
  transition: var(--transition);
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.result-card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.result-card-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.result-card-icon.presentation {
  background: var(--primary-color);
}

.result-card-icon.documentation {
  background: var(--success-color);
}

.result-card-icon.visualization {
  background: var(--warning-color);
}

.result-card-icon.data {
  background: var(--secondary-color);
}

.result-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.result-card-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.result-card-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.btn-secondary {
  background: var(--border-color);
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-secondary:hover {
  background: var(--text-secondary);
  color: white;
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.toast {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  padding: 1rem 1.5rem;
  max-width: 400px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-left: 4px solid var(--primary-color);
  animation: slideIn 0.3s ease-out;
}

.toast.success {
  border-left-color: var(--success-color);
}

.toast.warning {
  border-left-color: var(--warning-color);
}

.toast.error {
  border-left-color: var(--error-color);
}

.toast-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.toast.success .toast-icon {
  color: var(--success-color);
}

.toast.warning .toast-icon {
  color: var(--warning-color);
}

.toast.error .toast-icon {
  color: var(--error-color);
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.toast-message {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.toast-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: var(--transition);
}

.toast-close:hover {
  background: var(--border-color);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  padding: 3rem;
  text-align: center;
  max-width: 400px;
  margin: 1rem;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1.5rem;
}

.loading-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.loading-content p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Responsive Components */
@media (max-width: 768px) {
  .progress-steps {
    grid-template-columns: 1fr;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .result-card-actions {
    flex-direction: column;
  }
  
  .toast-container {
    left: 1rem;
    right: 1rem;
  }
  
  .toast {
    max-width: none;
  }
  
  .loading-content {
    padding: 2rem;
  }
}
