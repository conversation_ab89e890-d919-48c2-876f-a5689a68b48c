// ROBO-RESEARCHER-2000 API Communication
class APIManager {
    constructor() {
        // Use configuration from environment or fallback to defaults
        const config = window.ROBO_RESEARCHER_CONFIG || {};

        this.baseUrls = {
            n8n: config.apiBaseUrl || 'http://localhost:5678',
            minio: 'http://localhost:9000',
            wikijs: 'http://localhost:3000'
        };

        this.endpoints = {
            webhook: '/webhook/robo-researcher',
            testWebhook: '/webhook/test-robo-researcher',
            workflowStatus: '/api/v1/executions',
            minioDownload: '/robo-researcher-data',
            wikijsGraphQL: '/graphql'
        };

        // Full webhook URLs for convenience
        this.webhookURL = config.n8nWebhookUrl || `${this.baseUrls.n8n}${this.endpoints.webhook}`;
        this.testWebhookURL = config.n8nTestWebhookUrl || `${this.baseUrls.n8n}${this.endpoints.testWebhook}`;
    }
    
    // Workflow Management
    async triggerWorkflow(payload) {
        try {
            const response = await fetch(this.webhookURL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(payload)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            return {
                success: true,
                data: result,
                workflowId: result.executionId || this.generateTempId()
            };
            
        } catch (error) {
            console.error('Error triggering workflow:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Test Workflow Management
    async triggerTestWorkflow(payload) {
        try {
            const response = await fetch(this.testWebhookURL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            return {
                success: true,
                data: result,
                workflowId: result.executionId || this.generateTempId()
            };

        } catch (error) {
            console.error('Error triggering test workflow:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getWorkflowStatus(workflowId) {
        try {
            const response = await fetch(`${this.baseUrls.n8n}${this.endpoints.workflowStatus}/${workflowId}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            return {
                success: true,
                status: result.finished ? 'completed' : 'running',
                currentStep: result.data?.currentStep || 0,
                progress: result.data?.progress || 0,
                error: result.data?.error || null
            };
            
        } catch (error) {
            console.error('Error getting workflow status:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    // File Management
    async downloadFile(fileType, projectName) {
        const fileMap = {
            presentation: `${projectName}/presentations/${projectName}.pptx`,
            visualizations: `${projectName}/visualizations/affinity-map.svg`,
            data: `${projectName}/results/analysis-data.json`,
            documentation: `${projectName}/results/documentation.md`
        };
        
        const filePath = fileMap[fileType];
        if (!filePath) {
            throw new Error(`Unknown file type: ${fileType}`);
        }
        
        try {
            const downloadUrl = `${this.baseUrls.minio}${this.endpoints.minioDownload}/${filePath}`;
            
            // Create download link
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filePath.split('/').pop();
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            return { success: true };
            
        } catch (error) {
            console.error('Error downloading file:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    async getFileUrl(fileType, projectName) {
        const fileMap = {
            presentation: `${projectName}/presentations/${projectName}.pptx`,
            visualizations: `${projectName}/visualizations/affinity-map.svg`,
            data: `${projectName}/results/analysis-data.json`
        };
        
        const filePath = fileMap[fileType];
        if (!filePath) {
            throw new Error(`Unknown file type: ${fileType}`);
        }
        
        return `${this.baseUrls.minio}${this.endpoints.minioDownload}/${filePath}`;
    }
    
    // Wiki.js Integration
    async getWikiUrl(projectName) {
        return `${this.baseUrls.wikijs}/research/${projectName.toLowerCase().replace(/\s+/g, '-')}`;
    }
    
    async createWikiPage(projectName, content) {
        const query = `
            mutation {
                pages {
                    create(
                        content: "${content.replace(/"/g, '\\"')}"
                        description: "UX Analysis generated by ROBO-RESEARCHER-2000"
                        editor: "markdown"
                        isPublished: true
                        isPrivate: false
                        locale: "en"
                        path: "research/${projectName.toLowerCase().replace(/\s+/g, '-')}"
                        tags: ["ux-research", "automated", "robo-researcher"]
                        title: "${projectName}"
                    ) {
                        responseResult {
                            succeeded
                            errorCode
                            slug
                            message
                        }
                        page {
                            id
                            path
                            title
                        }
                    }
                }
            }
        `;
        
        try {
            const response = await fetch(`${this.baseUrls.wikijs}${this.endpoints.wikijsGraphQL}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ query })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            return {
                success: result.data?.pages?.create?.responseResult?.succeeded || false,
                url: result.data?.pages?.create?.page?.path || null,
                error: result.data?.pages?.create?.responseResult?.message || null
            };
            
        } catch (error) {
            console.error('Error creating wiki page:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    // Health Checks
    async checkServiceHealth() {
        const services = {
            n8n: `${this.baseUrls.n8n}/healthz`,
            minio: `${this.baseUrls.minio}/minio/health/live`,
            wikijs: `${this.baseUrls.wikijs}/healthz`
        };
        
        const results = {};
        
        for (const [service, url] of Object.entries(services)) {
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    timeout: 5000
                });
                
                results[service] = {
                    status: response.ok ? 'healthy' : 'unhealthy',
                    responseTime: Date.now() - startTime
                };
                
            } catch (error) {
                results[service] = {
                    status: 'error',
                    error: error.message
                };
            }
        }
        
        return results;
    }
    
    // Utility Methods
    generateTempId() {
        return 'temp_' + Math.random().toString(36).substr(2, 9);
    }
    
    async uploadToMinIO(file, path) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            
            const response = await fetch(`${this.baseUrls.minio}/upload/${path}`, {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`Upload failed: ${response.statusText}`);
            }
            
            const result = await response.json();
            return {
                success: true,
                url: result.url,
                path: result.path
            };
            
        } catch (error) {
            console.error('Error uploading to MinIO:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    // Email Validation
    validateEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }
    
    // API Key Validation
    validateOpenRouterKey(key) {
        return key && key.startsWith('sk-or-') && key.length > 20;
    }
    
    // Error Handling
    handleAPIError(error, context = '') {
        console.error(`API Error ${context}:`, error);
        
        let userMessage = 'Connection error';

        if (error.message.includes('Failed to fetch')) {
            userMessage = 'Cannot connect to server. Verify that services are running.';
        } else if (error.message.includes('404')) {
            userMessage = 'Service not found. Check configuration.';
        } else if (error.message.includes('500')) {
            userMessage = 'Internal server error. Please try again.';
        } else if (error.message.includes('timeout')) {
            userMessage = 'Request timeout. Server may be overloaded.';
        }
        
        return {
            technical: error.message,
            user: userMessage,
            timestamp: new Date().toISOString()
        };
    }
    
    // Configuration
    updateBaseUrls(config) {
        Object.assign(this.baseUrls, config);
    }
    
    getConfiguration() {
        return {
            baseUrls: { ...this.baseUrls },
            endpoints: { ...this.endpoints }
        };
    }
}

// Export for use in other modules
window.APIManager = APIManager;
