// ROBO-RESEARCHER-2000 Main Application
class RoboResearcher {
    constructor() {
        this.config = {
            n8nWebhookUrl: 'http://localhost:5678/webhook/robo-researcher',
            pollInterval: 5000, // 5 seconds
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedFileTypes: ['.txt'],
            steps: [
                { id: 1, name: 'Webhook Trigger', description: 'Recibiendo datos del cliente' },
                { id: 2, name: 'Validate Input', description: 'Validando formato y contenido' },
                { id: 3, name: 'Upload to MinIO', description: 'Almacenando transcripción' },
                { id: 4, name: 'Text Preprocessing', description: 'Limpieza y anonimización' },
                { id: 5, name: 'Segmentation', description: 'División por tópicos' },
                { id: 6, name: 'Deductive Coding', description: 'Aplicando códigos predefinidos' },
                { id: 7, name: 'Open Coding AI', description: 'IA sugiere códigos emergentes' },
                { id: 8, name: 'Category Grouping', description: 'Agrupando códigos similares' },
                { id: 9, name: 'Affinity Mapping', description: 'Generando visualizaciones' },
                { id: 10, name: 'Quantitative Analysis', description: 'Calculando métricas y frecuencias' },
                { id: 11, name: 'Pattern Detection', description: 'IA detecta patrones' },
                { id: 12, name: 'Insight Generation', description: 'Generando insights estructurados' },
                { id: 13, name: 'Archetype Creation', description: 'Creando arquetipos de usuarios' },
                { id: 14, name: 'HMW Generation', description: 'Generando "How Might We" questions' },
                { id: 15, name: 'Opportunity Prioritization', description: 'Aplicando matriz RICE' },
                { id: 16, name: 'Presentation Generation', description: 'Generando presentación con Marp' },
                { id: 17, name: 'Documentation & Email', description: 'Documentando en Wiki.js y enviando email' }
            ]
        };
        
        this.state = {
            currentStep: 0,
            workflowId: null,
            isProcessing: false,
            results: null
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupFileUpload();
        this.setupCollapsibleSections();
        this.setupPasswordToggles();
        this.loadSavedConfig();
    }
    
    setupEventListeners() {
        // Form submission
        const form = document.getElementById('uploadForm');
        if (form) {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }
        
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', this.handleNavigation.bind(this));
        });
        
        // Auto-save form data
        document.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('change', this.saveFormData.bind(this));
        });
    }
    
    setupFileUpload() {
        const fileInput = document.getElementById('transcriptionFile');
        const uploadArea = document.getElementById('fileUploadArea');
        const fileInfo = document.getElementById('fileInfo');
        
        if (!fileInput || !uploadArea) return;
        
        // Click to upload
        uploadArea.addEventListener('click', () => fileInput.click());
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelection(files[0]);
            }
        });
        
        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileSelection(e.target.files[0]);
            }
        });
    }
    
    handleFileSelection(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileInput = document.getElementById('transcriptionFile');
        
        // Validate file
        const validation = this.validateFile(file);
        if (!validation.valid) {
            this.showNotification('error', 'Archivo inválido', validation.message);
            return;
        }
        
        // Update UI
        fileInfo.innerHTML = `
            <i class="fas fa-file-text"></i>
            <span><strong>${file.name}</strong> (${this.formatFileSize(file.size)})</span>
            <button type="button" class="btn-secondary" onclick="app.removeFile()">
                <i class="fas fa-times"></i>
            </button>
        `;
        fileInfo.classList.remove('hidden');
        
        // Set file to input (for form submission)
        const dt = new DataTransfer();
        dt.items.add(file);
        fileInput.files = dt.files;
        
        this.showNotification('success', 'Archivo cargado', `${file.name} listo para análisis`);
    }
    
    validateFile(file) {
        // Check file type
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        if (!this.config.allowedFileTypes.includes(extension)) {
            return {
                valid: false,
                message: `Solo se permiten archivos ${this.config.allowedFileTypes.join(', ')}`
            };
        }
        
        // Check file size
        if (file.size > this.config.maxFileSize) {
            return {
                valid: false,
                message: `El archivo es demasiado grande. Máximo ${this.formatFileSize(this.config.maxFileSize)}`
            };
        }
        
        return { valid: true };
    }
    
    removeFile() {
        const fileInput = document.getElementById('transcriptionFile');
        const fileInfo = document.getElementById('fileInfo');
        
        fileInput.value = '';
        fileInfo.classList.add('hidden');
        
        this.showNotification('info', 'Archivo removido', 'Selecciona otro archivo para continuar');
    }
    
    setupCollapsibleSections() {
        document.querySelectorAll('.collapsible-header').forEach(header => {
            header.addEventListener('click', () => {
                const section = header.closest('.collapsible');
                section.classList.toggle('collapsed');
            });
        });
    }
    
    setupPasswordToggles() {
        document.querySelectorAll('.toggle-password').forEach(toggle => {
            toggle.addEventListener('click', () => {
                const targetId = toggle.dataset.target;
                const input = document.getElementById(targetId);
                
                if (input.type === 'password') {
                    input.type = 'text';
                    toggle.classList.remove('fa-eye');
                    toggle.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    toggle.classList.remove('fa-eye-slash');
                    toggle.classList.add('fa-eye');
                }
            });
        });
    }
    
    async handleFormSubmit(e) {
        e.preventDefault();
        
        if (this.state.isProcessing) {
            this.showNotification('warning', 'Procesando', 'Ya hay un análisis en curso');
            return;
        }
        
        const formData = this.getFormData();
        const validation = this.validateFormData(formData);
        
        if (!validation.valid) {
            this.showNotification('error', 'Datos incompletos', validation.message);
            return;
        }
        
        try {
            this.state.isProcessing = true;
            this.showLoadingOverlay('Iniciando análisis...');
            this.showProgressSection();
            
            // Start workflow
            const response = await this.startWorkflow(formData);
            
            if (response.success) {
                this.state.workflowId = response.workflowId;
                this.startProgressPolling();
                this.showNotification('success', 'Análisis iniciado', 'Tu transcripción está siendo procesada');
            } else {
                throw new Error(response.message || 'Error al iniciar el workflow');
            }
            
        } catch (error) {
            console.error('Error starting workflow:', error);
            this.showNotification('error', 'Error', error.message);
            this.state.isProcessing = false;
            this.hideLoadingOverlay();
        }
    }
    
    getFormData() {
        const form = document.getElementById('uploadForm');
        const formData = new FormData(form);
        
        // Convert to object
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        // Add file content
        const fileInput = document.getElementById('transcriptionFile');
        if (fileInput.files.length > 0) {
            data.file = fileInput.files[0];
        }
        
        return data;
    }
    
    validateFormData(data) {
        const required = ['projectName', 'email', 'studyType', 'openrouterKey'];
        const missing = required.filter(field => !data[field]);
        
        if (missing.length > 0) {
            return {
                valid: false,
                message: `Campos requeridos: ${missing.join(', ')}`
            };
        }
        
        if (!data.file) {
            return {
                valid: false,
                message: 'Debes seleccionar un archivo de transcripción'
            };
        }
        
        // Validate email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            return {
                valid: false,
                message: 'Email inválido'
            };
        }
        
        return { valid: true };
    }
    
    async startWorkflow(formData) {
        // Read file content
        const fileContent = await this.readFileAsText(formData.file);
        
        // Prepare payload
        const payload = {
            project_name: formData.projectName,
            email: formData.email,
            transcription: fileContent,
            study_type: formData.studyType,
            objectives: formData.objectives || '',
            language: formData.language || 'es',
            analysis_depth: formData.analysisDepth || 'standard',
            api_keys: {
                openrouter: formData.openrouterKey,
                smtp_password: formData.smtpPassword || ''
            },
            options: {
                enable_sentiment: formData.enableSentiment === 'on',
                enable_entities: formData.enableEntities === 'on',
                generate_presentation: formData.generatePresentation === 'on'
            },
            timestamp: new Date().toISOString()
        };
        
        // Send to n8n webhook
        const response = await fetch(this.config.n8nWebhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    }
    
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = e => reject(new Error('Error reading file'));
            reader.readAsText(file);
        });
    }
    
    showProgressSection() {
        document.getElementById('upload').style.display = 'none';
        document.getElementById('progress').classList.remove('hidden');
        this.renderProgressSteps();
    }

    renderProgressSteps() {
        const container = document.getElementById('progressSteps');
        container.innerHTML = this.config.steps.map(step => `
            <div class="progress-step" data-step="${step.id}">
                <div class="step-icon">${step.id}</div>
                <div class="step-content">
                    <div class="step-title">${step.name}</div>
                    <div class="step-description">${step.description}</div>
                </div>
            </div>
        `).join('');
    }

    startProgressPolling() {
        this.pollInterval = setInterval(() => {
            this.checkWorkflowProgress();
        }, this.config.pollInterval);
    }

    async checkWorkflowProgress() {
        try {
            // Simulate progress for demo (replace with actual API call)
            this.simulateProgress();
        } catch (error) {
            console.error('Error checking progress:', error);
            this.showNotification('error', 'Error', 'Error al verificar progreso');
        }
    }

    simulateProgress() {
        if (this.state.currentStep < this.config.steps.length) {
            this.state.currentStep++;
            this.updateProgressUI();

            if (this.state.currentStep >= this.config.steps.length) {
                this.completeWorkflow();
            }
        }
    }

    updateProgressUI() {
        const progress = (this.state.currentStep / this.config.steps.length) * 100;
        document.getElementById('progressFill').style.width = `${progress}%`;

        const currentStepName = this.config.steps[this.state.currentStep - 1]?.name || 'Completado';
        document.getElementById('progressText').textContent = `Paso ${this.state.currentStep}/${this.config.steps.length}: ${currentStepName}`;

        // Update step indicators
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            step.classList.remove('active', 'completed');
            if (index < this.state.currentStep - 1) {
                step.classList.add('completed');
            } else if (index === this.state.currentStep - 1) {
                step.classList.add('active');
            }
        });
    }

    completeWorkflow() {
        clearInterval(this.pollInterval);
        this.state.isProcessing = false;
        this.hideLoadingOverlay();

        // Show results
        this.showResultsSection();
        this.showNotification('success', 'Análisis completado', 'Tu investigación UX ha sido procesada exitosamente');
    }

    showResultsSection() {
        document.getElementById('progress').style.display = 'none';
        document.getElementById('results').classList.remove('hidden');
        this.renderResults();
    }

    renderResults() {
        const container = document.getElementById('resultsContainer');
        container.innerHTML = `
            <div class="results-grid">
                <div class="result-card">
                    <div class="result-card-header">
                        <div class="result-card-icon presentation">
                            <i class="fas fa-presentation"></i>
                        </div>
                        <div class="result-card-title">Presentación Final</div>
                    </div>
                    <div class="result-card-description">
                        Presentación PPTX generada automáticamente con insights, arquetipos y oportunidades.
                    </div>
                    <div class="result-card-actions">
                        <button class="btn btn-primary" onclick="app.downloadFile('presentation')">
                            <i class="fas fa-download"></i> Descargar PPTX
                        </button>
                        <button class="btn btn-outline" onclick="app.previewFile('presentation')">
                            <i class="fas fa-eye"></i> Vista previa
                        </button>
                    </div>
                </div>

                <div class="result-card">
                    <div class="result-card-header">
                        <div class="result-card-icon documentation">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="result-card-title">Documentación</div>
                    </div>
                    <div class="result-card-description">
                        Documentación completa en Wiki.js con insights detallados y metodología.
                    </div>
                    <div class="result-card-actions">
                        <button class="btn btn-primary" onclick="app.openWiki()">
                            <i class="fas fa-external-link-alt"></i> Abrir Wiki.js
                        </button>
                    </div>
                </div>

                <div class="result-card">
                    <div class="result-card-header">
                        <div class="result-card-icon visualization">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="result-card-title">Visualizaciones</div>
                    </div>
                    <div class="result-card-description">
                        Mapas de afinidad y diagramas generados automáticamente en formato SVG.
                    </div>
                    <div class="result-card-actions">
                        <button class="btn btn-primary" onclick="app.downloadFile('visualizations')">
                            <i class="fas fa-download"></i> Descargar SVG
                        </button>
                        <button class="btn btn-outline" onclick="app.previewFile('visualizations')">
                            <i class="fas fa-eye"></i> Ver online
                        </button>
                    </div>
                </div>

                <div class="result-card">
                    <div class="result-card-header">
                        <div class="result-card-icon data">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="result-card-title">Datos de Análisis</div>
                    </div>
                    <div class="result-card-description">
                        Datos estructurados de codificación, insights y métricas en formato JSON.
                    </div>
                    <div class="result-card-actions">
                        <button class="btn btn-primary" onclick="app.downloadFile('data')">
                            <i class="fas fa-download"></i> Descargar JSON
                        </button>
                    </div>
                </div>
            </div>

            <div class="text-center" style="margin-top: 2rem;">
                <button class="btn btn-outline" onclick="app.startNewAnalysis()">
                    <i class="fas fa-plus"></i> Nuevo Análisis
                </button>
            </div>
        `;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    saveFormData() {
        const formData = this.getFormData();
        localStorage.setItem('robo-researcher-form', JSON.stringify(formData));
    }
    
    loadSavedConfig() {
        const saved = localStorage.getItem('robo-researcher-form');
        if (saved) {
            try {
                const data = JSON.parse(saved);
                // Restore form fields (except sensitive data)
                Object.keys(data).forEach(key => {
                    if (key !== 'openrouterKey' && key !== 'smtpPassword') {
                        const element = document.getElementById(key);
                        if (element && element.type !== 'file') {
                            element.value = data[key];
                        }
                    }
                });
            } catch (error) {
                console.warn('Error loading saved config:', error);
            }
        }
    }

    // Additional utility methods
    showLoadingOverlay(message) {
        const overlay = document.getElementById('loadingOverlay');
        const messageElement = document.getElementById('loadingMessage');

        if (messageElement) messageElement.textContent = message;
        if (overlay) overlay.classList.remove('hidden');
    }

    hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) overlay.classList.add('hidden');
    }

    handleNavigation(e) {
        e.preventDefault();
        const target = e.target.getAttribute('href');
        if (target) {
            document.querySelector(target)?.scrollIntoView({ behavior: 'smooth' });
        }
    }

    // File action handlers
    downloadFile(type) {
        const projectName = document.getElementById('projectName')?.value || 'proyecto';
        this.showNotification('info', 'Descargando...', `Preparando ${type} para descarga`);

        // Simulate download (replace with actual API call)
        setTimeout(() => {
            this.showNotification('success', 'Descarga completada', `${type} descargado exitosamente`);
        }, 2000);
    }

    previewFile(type) {
        this.showNotification('info', 'Abriendo vista previa...', `Cargando ${type}`);
        // Implement preview functionality
    }

    openWiki() {
        const projectName = document.getElementById('projectName')?.value || 'proyecto';
        const wikiUrl = `http://localhost:3000/research/${projectName.toLowerCase().replace(/\s+/g, '-')}`;
        window.open(wikiUrl, '_blank');
    }

    startNewAnalysis() {
        // Reset form and state
        this.state = {
            currentStep: 0,
            workflowId: null,
            isProcessing: false,
            results: null
        };

        // Show upload section
        document.getElementById('results').classList.add('hidden');
        document.getElementById('progress').classList.add('hidden');
        document.getElementById('upload').style.display = 'block';

        // Reset form
        document.getElementById('uploadForm').reset();
        this.removeFile();

        this.showNotification('info', 'Nuevo análisis', 'Formulario reiniciado para nuevo proyecto');
    }

    showNotification(type, title, message, options = {}) {
        if (window.notifications) {
            return window.notifications.show(type, title, message, options);
        } else {
            // Fallback to console if notifications not available
            console.log(`${type.toUpperCase()}: ${title} - ${message}`);
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new RoboResearcher();
});
