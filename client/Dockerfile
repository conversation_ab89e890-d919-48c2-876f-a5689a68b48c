# ROBO-RESEARCHER-2000 Client Application Dockerfile
# Multi-stage build for optimized production image

# Stage 1: Build stage (if needed for future enhancements)
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files (if we add build tools later)
# COPY package*.json ./
# RUN npm ci --only=production

# Copy client source files
COPY . .

# Stage 2: Production stage with Nginx
FROM nginx:alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Remove default nginx website
RUN rm -rf /usr/share/nginx/html/*

# Copy client application files
COPY --from=builder /app /usr/share/nginx/html

# Create nginx configuration for SPA
RUN cat > /etc/nginx/conf.d/default.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Main application
    location / {
        try_files $uri $uri/ /index.html;
        
        # No cache for HTML files
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # API proxy to n8n (optional, for CORS handling)
    location /api/ {
        proxy_pass http://robo-researcher-n8n:5678/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
    }

    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
EOF

# Create startup script to handle environment variables
RUN cat > /docker-entrypoint.sh << 'EOF'
#!/bin/sh

# Replace environment variables in JavaScript files
if [ -n "$N8N_WEBHOOK_URL" ]; then
    find /usr/share/nginx/html -name "*.js" -type f -exec sed -i "s|http://localhost:5678/webhook/robo-researcher|$N8N_WEBHOOK_URL|g" {} \;
fi

if [ -n "$N8N_TEST_WEBHOOK_URL" ]; then
    find /usr/share/nginx/html -name "*.js" -type f -exec sed -i "s|http://localhost:5678/webhook/test-robo-researcher|$N8N_TEST_WEBHOOK_URL|g" {} \;
fi

if [ -n "$API_BASE_URL" ]; then
    find /usr/share/nginx/html -name "*.js" -type f -exec sed -i "s|http://localhost:5678|$API_BASE_URL|g" {} \;
fi

# Create config.js with environment variables
cat > /usr/share/nginx/html/js/config.js << JSEOF
// Auto-generated configuration from environment variables
window.ROBO_RESEARCHER_CONFIG = {
    n8nWebhookUrl: '${N8N_WEBHOOK_URL:-http://localhost:5678/webhook/robo-researcher}',
    n8nTestWebhookUrl: '${N8N_TEST_WEBHOOK_URL:-http://localhost:5678/webhook/test-robo-researcher}',
    apiBaseUrl: '${API_BASE_URL:-http://localhost:5678}',
    version: '1.0.0',
    environment: '${NODE_ENV:-production}'
};
JSEOF

# Start nginx
exec nginx -g 'daemon off;'
EOF

# Make startup script executable
RUN chmod +x /docker-entrypoint.sh

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Use custom entrypoint
ENTRYPOINT ["/docker-entrypoint.sh"]

# Labels for metadata
LABEL maintainer="ROBO-RESEARCHER-2000 Team"
LABEL description="Client web application for ROBO-RESEARCHER-2000 UX research analysis system"
LABEL version="1.0.0"
