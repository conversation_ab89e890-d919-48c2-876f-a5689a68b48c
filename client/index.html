<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROBO-RESEARCHER-2000 | Automated UX Research</title>
    <meta name="description" content="Semi-automated UX research system that transforms transcriptions into actionable insights">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/icons/robot.svg">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>ROBO-RESEARCHER-2000</h1>
                </div>
                <nav class="nav">
                    <a href="#upload" class="nav-link">Upload</a>
                    <a href="#config" class="nav-link">Configure</a>
                    <a href="#results" class="nav-link">Results</a>
                    <a href="#help" class="nav-link">Help</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h2>Transform transcriptions into actionable insights</h2>
                    <p>Semi-automated UX research system that analyzes user interviews and generates professional presentations in minutes.</p>
                    <div class="hero-stats">
                        <div class="stat">
                            <span class="stat-number">17</span>
                            <span class="stat-label">Automated steps</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">15-20</span>
                            <span class="stat-label">Processing minutes</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Open source</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Upload Section -->
        <section id="upload" class="section">
            <div class="container">
                <div class="section-header">
                    <h3><i class="fas fa-upload"></i> Upload Transcription</h3>
                    <p>Upload your transcription file and configure analysis parameters</p>
                </div>
                
                <div class="upload-form-container">
                    <form id="uploadForm" class="upload-form">
                        <!-- Project Info -->
                        <div class="form-group">
                            <label for="projectName">Project Name</label>
                            <input type="text" id="projectName" name="projectName" required
                                   placeholder="e.g., Mobile App Research Q1 2025">
                        </div>

                        <div class="form-group">
                            <label for="email">Email for Results</label>
                            <input type="email" id="email" name="email" required
                                   placeholder="<EMAIL>">
                        </div>

                        <!-- File Upload -->
                        <div class="form-group">
                            <label for="transcriptionFile">Transcription File (.txt)</label>
                            <div class="file-upload-area" id="fileUploadArea">
                                <input type="file" id="transcriptionFile" name="transcriptionFile"
                                       accept=".txt" required hidden>
                                <div class="file-upload-content">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>Drag your .txt file here or <span class="upload-link">click to select</span></p>
                                    <small>Maximum 10MB</small>
                                </div>
                            </div>
                            <div id="fileInfo" class="file-info hidden"></div>
                        </div>

                        <!-- Study Context -->
                        <div class="form-group">
                            <label for="studyType">Study Type</label>
                            <select id="studyType" name="studyType" required>
                                <option value="">Select type</option>
                                <option value="user_interview">User Interview</option>
                                <option value="usability_test">Usability Test</option>
                                <option value="focus_group">Focus Group</option>
                                <option value="contextual_inquiry">Contextual Inquiry</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="objectives">Research Objectives</label>
                            <textarea id="objectives" name="objectives" rows="3"
                                      placeholder="Describe the main objectives of this research..."></textarea>
                        </div>

                        <!-- API Configuration -->
                        <div class="form-section">
                            <h4><i class="fas fa-cog"></i> API Configuration</h4>

                            <div class="form-group">
                                <label for="openrouterKey">OpenRouter API Key</label>
                                <div class="input-with-icon">
                                    <input type="password" id="openrouterKey" name="openrouterKey"
                                           placeholder="sk-or-...">
                                    <i class="fas fa-eye toggle-password" data-target="openrouterKey"></i>
                                </div>
                                <small>For AI analysis. <a href="https://openrouter.ai" target="_blank">Get API Key</a></small>
                            </div>

                            <div class="form-group">
                                <label for="smtpPassword">SMTP Password (optional)</label>
                                <div class="input-with-icon">
                                    <input type="password" id="smtpPassword" name="smtpPassword"
                                           placeholder="Password for email delivery">
                                    <i class="fas fa-eye toggle-password" data-target="smtpPassword"></i>
                                </div>
                                <small>For automatic email delivery of results</small>
                            </div>
                        </div>

                        <!-- Advanced Options -->
                        <div class="form-section collapsible">
                            <h4 class="collapsible-header">
                                <i class="fas fa-sliders-h"></i> Advanced Options
                                <i class="fas fa-chevron-down"></i>
                            </h4>
                            <div class="collapsible-content">
                                <div class="form-group">
                                    <label for="language">Analysis Language</label>
                                    <select id="language" name="language">
                                        <option value="en">English</option>
                                        <option value="es">Español</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="analysisDepth">Analysis Depth</label>
                                    <select id="analysisDepth" name="analysisDepth">
                                        <option value="standard">Standard (recommended)</option>
                                        <option value="deep">Deep (more time)</option>
                                        <option value="quick">Quick (less detail)</option>
                                    </select>
                                </div>

                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableSentiment" name="enableSentiment" checked>
                                        <span class="checkmark"></span>
                                        Sentiment analysis
                                    </label>

                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableEntities" name="enableEntities" checked>
                                        <span class="checkmark"></span>
                                        Entity extraction
                                    </label>

                                    <label class="checkbox-label">
                                        <input type="checkbox" id="generatePresentation" name="generatePresentation" checked>
                                        <span class="checkmark"></span>
                                        Generate automatic presentation
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-rocket"></i>
                                Start Analysis
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <!-- Progress Section -->
        <section id="progress" class="section hidden">
            <div class="container">
                <div class="section-header">
                    <h3><i class="fas fa-tasks"></i> Analysis Progress</h3>
                    <p>Your transcription is being processed...</p>
                </div>

                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Starting analysis...</div>

                    <div class="progress-steps" id="progressSteps">
                        <!-- Steps will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Results Section -->
        <section id="results" class="section hidden">
            <div class="container">
                <div class="section-header">
                    <h3><i class="fas fa-chart-line"></i> Results</h3>
                    <p>Your analysis has been completed successfully</p>
                </div>

                <div class="results-container" id="resultsContainer">
                    <!-- Results will be populated by JavaScript -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>ROBO-RESEARCHER-2000</h4>
                    <p>Automated UX research system with open-source tools.</p>
                </div>
                <div class="footer-section">
                    <h4>Links</h4>
                    <ul>
                        <li><a href="https://github.com/your-username/robo-researcher-2000">GitHub</a></li>
                        <li><a href="#help">Documentation</a></li>
                        <li><a href="#contact">Support</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Technologies</h4>
                    <ul>
                        <li>n8n Workflows</li>
                        <li>Python + NLTK/spaCy</li>
                        <li>MinIO Storage</li>
                        <li>Wiki.js Docs</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ROBO-RESEARCHER-2000. Open Source under MIT license.</p>
            </div>
        </div>
    </footer>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>Processing analysis...</h3>
            <p id="loadingMessage">Starting UX research workflow</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
    <script src="js/upload.js"></script>
    <script src="js/api.js"></script>
    <script src="js/notifications.js"></script>
</body>
</html>
