<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROBO-RESEARCHER-2000 | Investigación UX Automatizada</title>
    <meta name="description" content="Sistema de investigación UX semi-automatizado que transforma transcripciones en insights accionables">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/icons/robot.svg">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>ROBO-RESEARCHER-2000</h1>
                </div>
                <nav class="nav">
                    <a href="#upload" class="nav-link">Subir</a>
                    <a href="#config" class="nav-link">Configurar</a>
                    <a href="#results" class="nav-link">Resultados</a>
                    <a href="#help" class="nav-link">Ayuda</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h2>Transforma transcripciones en insights accionables</h2>
                    <p>Sistema de investigación UX semi-automatizado que analiza entrevistas con usuarios y genera presentaciones profesionales en minutos.</p>
                    <div class="hero-stats">
                        <div class="stat">
                            <span class="stat-number">17</span>
                            <span class="stat-label">Pasos automatizados</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">15-20</span>
                            <span class="stat-label">Minutos de procesamiento</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Open source</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Upload Section -->
        <section id="upload" class="section">
            <div class="container">
                <div class="section-header">
                    <h3><i class="fas fa-upload"></i> Subir Transcripción</h3>
                    <p>Sube tu archivo de transcripción y configura los parámetros del análisis</p>
                </div>
                
                <div class="upload-form-container">
                    <form id="uploadForm" class="upload-form">
                        <!-- Project Info -->
                        <div class="form-group">
                            <label for="projectName">Nombre del Proyecto</label>
                            <input type="text" id="projectName" name="projectName" required 
                                   placeholder="Ej: Investigación App Móvil Q1 2025">
                        </div>

                        <div class="form-group">
                            <label for="email">Email para Resultados</label>
                            <input type="email" id="email" name="email" required 
                                   placeholder="<EMAIL>">
                        </div>

                        <!-- File Upload -->
                        <div class="form-group">
                            <label for="transcriptionFile">Archivo de Transcripción (.txt)</label>
                            <div class="file-upload-area" id="fileUploadArea">
                                <input type="file" id="transcriptionFile" name="transcriptionFile" 
                                       accept=".txt" required hidden>
                                <div class="file-upload-content">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>Arrastra tu archivo .txt aquí o <span class="upload-link">haz clic para seleccionar</span></p>
                                    <small>Máximo 10MB</small>
                                </div>
                            </div>
                            <div id="fileInfo" class="file-info hidden"></div>
                        </div>

                        <!-- Study Context -->
                        <div class="form-group">
                            <label for="studyType">Tipo de Estudio</label>
                            <select id="studyType" name="studyType" required>
                                <option value="">Seleccionar tipo</option>
                                <option value="user_interview">Entrevista de Usuario</option>
                                <option value="usability_test">Prueba de Usabilidad</option>
                                <option value="focus_group">Grupo Focal</option>
                                <option value="contextual_inquiry">Investigación Contextual</option>
                                <option value="other">Otro</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="objectives">Objetivos de la Investigación</label>
                            <textarea id="objectives" name="objectives" rows="3" 
                                      placeholder="Describe los objetivos principales de esta investigación..."></textarea>
                        </div>

                        <!-- API Configuration -->
                        <div class="form-section">
                            <h4><i class="fas fa-cog"></i> Configuración de APIs</h4>
                            
                            <div class="form-group">
                                <label for="openrouterKey">OpenRouter API Key</label>
                                <div class="input-with-icon">
                                    <input type="password" id="openrouterKey" name="openrouterKey" 
                                           placeholder="sk-or-...">
                                    <i class="fas fa-eye toggle-password" data-target="openrouterKey"></i>
                                </div>
                                <small>Para análisis con IA. <a href="https://openrouter.ai" target="_blank">Obtener API Key</a></small>
                            </div>

                            <div class="form-group">
                                <label for="smtpPassword">Contraseña SMTP (opcional)</label>
                                <div class="input-with-icon">
                                    <input type="password" id="smtpPassword" name="smtpPassword" 
                                           placeholder="Contraseña para envío de emails">
                                    <i class="fas fa-eye toggle-password" data-target="smtpPassword"></i>
                                </div>
                                <small>Para envío automático de resultados por email</small>
                            </div>
                        </div>

                        <!-- Advanced Options -->
                        <div class="form-section collapsible">
                            <h4 class="collapsible-header">
                                <i class="fas fa-sliders-h"></i> Opciones Avanzadas
                                <i class="fas fa-chevron-down"></i>
                            </h4>
                            <div class="collapsible-content">
                                <div class="form-group">
                                    <label for="language">Idioma del Análisis</label>
                                    <select id="language" name="language">
                                        <option value="es">Español</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="analysisDepth">Profundidad del Análisis</label>
                                    <select id="analysisDepth" name="analysisDepth">
                                        <option value="standard">Estándar (recomendado)</option>
                                        <option value="deep">Profundo (más tiempo)</option>
                                        <option value="quick">Rápido (menos detalle)</option>
                                    </select>
                                </div>

                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableSentiment" name="enableSentiment" checked>
                                        <span class="checkmark"></span>
                                        Análisis de sentimientos
                                    </label>
                                    
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableEntities" name="enableEntities" checked>
                                        <span class="checkmark"></span>
                                        Extracción de entidades
                                    </label>
                                    
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="generatePresentation" name="generatePresentation" checked>
                                        <span class="checkmark"></span>
                                        Generar presentación automática
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-rocket"></i>
                                Iniciar Análisis
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <!-- Progress Section -->
        <section id="progress" class="section hidden">
            <div class="container">
                <div class="section-header">
                    <h3><i class="fas fa-tasks"></i> Progreso del Análisis</h3>
                    <p>Tu transcripción está siendo procesada...</p>
                </div>
                
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Iniciando análisis...</div>
                    
                    <div class="progress-steps" id="progressSteps">
                        <!-- Steps will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Results Section -->
        <section id="results" class="section hidden">
            <div class="container">
                <div class="section-header">
                    <h3><i class="fas fa-chart-line"></i> Resultados</h3>
                    <p>Tu análisis ha sido completado exitosamente</p>
                </div>
                
                <div class="results-container" id="resultsContainer">
                    <!-- Results will be populated by JavaScript -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>ROBO-RESEARCHER-2000</h4>
                    <p>Sistema de investigación UX automatizado con herramientas open-source.</p>
                </div>
                <div class="footer-section">
                    <h4>Enlaces</h4>
                    <ul>
                        <li><a href="https://github.com/tu-usuario/robo-researcher-2000">GitHub</a></li>
                        <li><a href="#help">Documentación</a></li>
                        <li><a href="#contact">Soporte</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Tecnologías</h4>
                    <ul>
                        <li>n8n Workflows</li>
                        <li>Python + NLTK/spaCy</li>
                        <li>MinIO Storage</li>
                        <li>Wiki.js Docs</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ROBO-RESEARCHER-2000. Open Source bajo licencia MIT.</p>
            </div>
        </div>
    </footer>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>Procesando análisis...</h3>
            <p id="loadingMessage">Iniciando workflow de investigación UX</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
    <script src="js/upload.js"></script>
    <script src="js/api.js"></script>
    <script src="js/notifications.js"></script>
</body>
</html>
