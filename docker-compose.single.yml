version: '3.8'

# ROBO-RESEARCHER-2000 Single Container Deployment
# Everything in one container for maximum simplicity

services:
  robo-researcher:
    image: robo-researcher-2000:single
    container_name: robo-researcher
    restart: unless-stopped
    
    # Port mappings
    ports:
      - "5678:5678"  # n8n web interface
      - "9001:9001"  # MinIO console
    
    # Environment variables (only API keys required)
    environment:
      # Required: AI API Key
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      
      # Required: Email Configuration
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      
      # Optional: Advanced Settings
      - SMTP_PORT=${SMTP_PORT:-587}
      - GENERIC_TIMEZONE=${GENERIC_TIMEZONE:-America/Mexico_City}
      - N8N_LOG_LEVEL=${N8N_LOG_LEVEL:-info}
    
    # Data persistence
    volumes:
      - robo-data:/opt/robo-researcher/data
      - robo-n8n:/home/<USER>/.n8n
      - robo-postgres:/var/lib/postgresql/14/main
    
    # Health check
    healthcheck:
      test: ["CMD", "/opt/robo-researcher/config/healthcheck.sh"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 120s
    
    # Resource limits (optional)
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# Named volumes for data persistence
volumes:
  robo-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/single-container
  
  robo-n8n:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/n8n-single
  
  robo-postgres:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres-single

# Network (using default bridge network)
networks:
  default:
    name: robo-researcher-single
