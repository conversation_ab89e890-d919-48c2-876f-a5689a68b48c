# 🤖 ROBO-RESEARCHER-2000 Single Container

**The simplest way to run ROBO-RESEARCHER-2000** - Everything in one container!

## 🚀 Quick Start

### 1. Get Your API Keys

You only need **2 things** to get started:

1. **OpenRouter API Key** (for AI functionality)
   - Get it free at: https://openrouter.ai/keys
   
2. **Email Credentials** (for notifications)
   - Gmail: Use app password (not your regular password)
   - Other SMTP: Any email provider

### 2. Run the Container

```bash
# Basic run with API keys
docker run -d \
  --name robo-researcher \
  -p 5678:5678 \
  -p 9001:9001 \
  -e OPENROUTER_API_KEY="your_openrouter_key_here" \
  -e SMTP_HOST="smtp.gmail.com" \
  -e SMTP_USER="<EMAIL>" \
  -e SMTP_PASSWORD="your_app_password" \
  -v robo-data:/opt/robo-researcher/data \
  robo-researcher-2000:single
```

### 3. Access Your System

- **🎯 n8n Workflow Engine**: http://localhost:5678
  - Username: `admin`
  - Password: `robo-researcher-2000`
  
- **📦 MinIO Storage Console**: http://localhost:9001
  - Username: `minioadmin`
  - Password: `minioadmin`

**That's it!** 🎉 Everything else is pre-configured.

## 📋 Environment Variables

### Required

| Variable | Description | Example |
|----------|-------------|---------|
| `OPENROUTER_API_KEY` | Your OpenRouter API key | `sk-or-v1-abc123...` |
| `SMTP_HOST` | Email server hostname | `smtp.gmail.com` |
| `SMTP_USER` | Email username | `<EMAIL>` |
| `SMTP_PASSWORD` | Email password/app password | `your_app_password` |

### Optional

| Variable | Default | Description |
|----------|---------|-------------|
| `SMTP_PORT` | `587` | Email server port |
| `GENERIC_TIMEZONE` | `America/Mexico_City` | Container timezone |
| `N8N_LOG_LEVEL` | `info` | Logging level |

## 🔧 Advanced Usage

### With Custom Configuration

```bash
docker run -d \
  --name robo-researcher \
  -p 5678:5678 \
  -p 9001:9001 \
  -e OPENROUTER_API_KEY="your_key" \
  -e SMTP_HOST="mail.yourcompany.com" \
  -e SMTP_PORT="465" \
  -e SMTP_USER="<EMAIL>" \
  -e SMTP_PASSWORD="your_password" \
  -e GENERIC_TIMEZONE="Europe/London" \
  -e N8N_LOG_LEVEL="debug" \
  -v robo-data:/opt/robo-researcher/data \
  -v robo-n8n:/home/<USER>/.n8n \
  robo-researcher-2000:single
```

### Using Docker Compose

Create `docker-compose.single.yml`:

```yaml
version: '3.8'

services:
  robo-researcher:
    image: robo-researcher-2000:single
    container_name: robo-researcher
    restart: unless-stopped
    ports:
      - "5678:5678"
      - "9001:9001"
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_PORT=${SMTP_PORT:-587}
      - GENERIC_TIMEZONE=${GENERIC_TIMEZONE:-America/Mexico_City}
    volumes:
      - robo-data:/opt/robo-researcher/data
      - robo-n8n:/home/<USER>/.n8n
    healthcheck:
      test: ["CMD", "/opt/robo-researcher/config/healthcheck.sh"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 120s

volumes:
  robo-data:
  robo-n8n:
```

Then run:

```bash
# Copy environment template
cp .env.single .env

# Edit your API keys
nano .env

# Start the container
docker-compose -f docker-compose.single.yml up -d
```

## 🛠 Management Commands

### Container Management

```bash
# Start container
docker start robo-researcher

# Stop container
docker stop robo-researcher

# View logs
docker logs -f robo-researcher

# Check health
docker exec robo-researcher /opt/robo-researcher/config/healthcheck.sh

# Access container shell
docker exec -it robo-researcher bash
```

### Service Management (Inside Container)

```bash
# Check all services
docker exec robo-researcher supervisorctl status

# Restart n8n
docker exec robo-researcher supervisorctl restart n8n

# View service logs
docker exec robo-researcher tail -f /var/log/supervisor/n8n.log
```

## 🔍 Troubleshooting

### Container Won't Start

1. **Check logs**: `docker logs robo-researcher`
2. **Verify API keys**: Make sure they're correctly set
3. **Check ports**: Ensure 5678 and 9001 are available

### Services Not Ready

```bash
# Wait for startup (can take 2-3 minutes)
docker logs -f robo-researcher

# Check individual services
docker exec robo-researcher supervisorctl status
```

### Can't Access n8n

1. **Wait for startup**: Initial startup takes 2-3 minutes
2. **Check health**: `docker exec robo-researcher /opt/robo-researcher/config/healthcheck.sh`
3. **Verify port**: Make sure http://localhost:5678 is accessible

### Email Not Working

1. **Check SMTP settings**: Verify host, user, password
2. **Gmail users**: Use app password, not regular password
3. **Test credentials**: Try logging into your email provider

### Data Persistence Issues

```bash
# Check volumes
docker volume ls | grep robo

# Backup data
docker run --rm -v robo-data:/data -v $(pwd):/backup alpine tar czf /backup/robo-backup.tar.gz /data
```

## 📊 What's Inside

The single container includes:

- **🧠 n8n**: Workflow automation engine
- **🗄️ PostgreSQL**: Database for n8n data
- **📦 MinIO**: S3-compatible storage
- **⚡ Redis**: Caching layer
- **🐍 Python**: For custom scripts
- **📋 Supervisor**: Process management

All services are **pre-configured** to work together with **zero configuration**!

## 🔒 Security Notes

- Default passwords are included for convenience
- Change them in production environments
- API keys are passed via environment variables (secure)
- Internal services only communicate via localhost
- Only n8n (5678) and MinIO console (9001) are exposed

## 🆘 Getting Help

1. **Check logs**: `docker logs robo-researcher`
2. **Health check**: `docker exec robo-researcher /opt/robo-researcher/config/healthcheck.sh`
3. **Service status**: `docker exec robo-researcher supervisorctl status`

For more help, check the main documentation or create an issue on GitHub.
