{"main": {"id": "2eb5a48093a8b0c0", "type": "split", "children": [{"id": "bd10316027b60eae", "type": "tabs", "children": [{"id": "11db0d7b22030c69", "type": "leaf", "state": {"type": "markdown", "state": {"file": "workflows/workflow-documentation.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "workflow-documentation"}}]}], "direction": "vertical"}, "left": {"id": "57c753ec279e35a2", "type": "split", "children": [{"id": "b2ed4091a809ef4f", "type": "tabs", "children": [{"id": "373a0748edd32b31", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "795ffa336ce898e5", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "d83a0853dfdd49de", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "8adbe328f1047e54", "type": "split", "children": [{"id": "a508caab8ff4c5a9", "type": "tabs", "children": [{"id": "b27a4d02896219e6", "type": "leaf", "state": {"type": "backlink", "state": {"file": "workflows/workflow-documentation.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for workflow-documentation"}}, {"id": "ebc1ba70f0d5ec66", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "workflows/workflow-documentation.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from workflow-documentation"}}, {"id": "b2c6cbbabe91c114", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "0e89653afc3f59ae", "type": "leaf", "state": {"type": "outline", "state": {"file": "workflows/workflow-documentation.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of workflow-documentation"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "373a0748edd32b31", "lastOpenFiles": ["DEPLOYMENT_SUCCESS_SUMMARY.md", "NUC2_DEPLOYMENT_PLAN.md", "client/nginx.conf", "client/Dockerfile", "DEPLOYMENT_ASSESSMENT.md", "PROJECT_OVERVIEW.md", "deploy.sh", "tests/final_comprehensive_test.py", "README.md", "docs/setup-guide.md", "docs/production-checklist.md", "docs/api-reference.md", "docs/user-manual.md", "docs/deployment-guide.md", "README-EN.md", "docs/integration-guide.md", "docs", "tests/run_tests.py", "workflows/setup-guide.md", "tests/deployment_validator.py", "tests/client_test.html", "tests/integration_test.py", "tests", "Implementation plan.md", "guiduelines for n8n mcp.md", "general plan.md", "workflows/workflow-documentation.md", "client/assets/icons/robot.svg", "workflows/config/presentation-template.md", "workflows/README.md", "infrastructure/README.md", "n8n_UX_Research_Workflow_Planning__2025-07-17T01-56-18.md"]}