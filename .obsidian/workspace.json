{"main": {"id": "2eb5a48093a8b0c0", "type": "split", "children": [{"id": "bd10316027b60eae", "type": "tabs", "children": [{"id": "11db0d7b22030c69", "type": "leaf", "state": {"type": "markdown", "state": {"file": "general plan.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "general plan"}}]}], "direction": "vertical"}, "left": {"id": "57c753ec279e35a2", "type": "split", "children": [{"id": "b2ed4091a809ef4f", "type": "tabs", "children": [{"id": "373a0748edd32b31", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "795ffa336ce898e5", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "d83a0853dfdd49de", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "8adbe328f1047e54", "type": "split", "children": [{"id": "a508caab8ff4c5a9", "type": "tabs", "children": [{"id": "b27a4d02896219e6", "type": "leaf", "state": {"type": "backlink", "state": {"file": "general plan.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for general plan"}}, {"id": "ebc1ba70f0d5ec66", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "general plan.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from general plan"}}, {"id": "b2c6cbbabe91c114", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "0e89653afc3f59ae", "type": "leaf", "state": {"type": "outline", "state": {"file": "general plan.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of general plan"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "11db0d7b22030c69", "lastOpenFiles": ["n8n_UX_Research_Workflow_Planning__2025-07-17T01-56-18.md", "Implementation plan.md", "general plan.md"]}