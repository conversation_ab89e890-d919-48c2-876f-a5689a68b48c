#!/bin/bash

# ROBO-RESEARCHER-2000 Direct Deployment to nuc2 Container 200
# This script connects directly to nuc2 and deploys the system

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_ID="200"
REPO_URL="https://github.com/c42705/robo-researcher-2000.git"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ROBO-RESEARCHER-2000                      ║"
    echo "║              Direct nuc2 Container Deployment                ║"
    echo "║                                                              ║"
    echo "║  Target: Container 200 (robo-researcher) on nuc2            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Main deployment function
deploy_to_container() {
    print_banner
    
    log_info "Starting direct deployment to nuc2 container 200..."
    
    # Create deployment script for container
    cat > /tmp/container_deploy.sh << 'DEPLOY_SCRIPT'
#!/bin/bash

set -e

echo "=== ROBO-RESEARCHER-2000 Container Deployment ==="

# Step 1: Update system
echo "Step 1: Updating system packages..."
apt update && apt upgrade -y

# Step 2: Install Docker and dependencies
echo "Step 2: Installing Docker and dependencies..."
apt install -y docker.io docker-compose git curl wget nano htop python3 python3-pip

# Step 3: Enable Docker
echo "Step 3: Enabling Docker service..."
systemctl enable docker
systemctl start docker

# Step 4: Clone repository
echo "Step 4: Cloning ROBO-RESEARCHER-2000 repository..."
if [ -d "/opt/robo-researcher-2000" ]; then
    rm -rf /opt/robo-researcher-2000
fi
git clone https://github.com/c42705/robo-researcher-2000.git /opt/robo-researcher-2000

# Step 5: Create environment configuration
echo "Step 5: Creating environment configuration..."
cat > /opt/robo-researcher-2000/.env << 'EOF'
# Core Database Configuration
POSTGRES_DB=robo_researcher
POSTGRES_USER=robo_user
POSTGRES_PASSWORD=robo_pass_2000

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000
N8N_HOST=robo.stargety.com
N8N_PROTOCOL=https
N8N_SECURE_COOKIE=false

# MinIO Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin

# Wiki.js Configuration
WIKIJS_DB_PASSWORD=wikijs_pass_2000

# Email Configuration (Stargety SMTP)
SMTP_HOST=mail.stargety.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# API Configuration
OPENROUTER_API_KEY=your_openrouter_key

# Client Configuration
N8N_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/robo-researcher
N8N_TEST_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/test-robo-researcher
API_BASE_URL=http://localhost:5678
NODE_ENV=production
EOF

# Step 6: Install Python dependencies
echo "Step 6: Installing Python dependencies..."
cd /opt/robo-researcher-2000
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
fi

# Step 7: Deploy Docker containers
echo "Step 7: Deploying Docker containers..."
cd /opt/robo-researcher-2000
docker-compose -f infrastructure/docker-compose.yml up -d --build

# Step 8: Wait for services
echo "Step 8: Waiting for services to start..."
sleep 60

# Step 9: Check deployment
echo "Step 9: Checking deployment..."
docker ps
echo ""
echo "=== Service Health Checks ==="
curl -f http://localhost:80/health && echo " - Client: OK" || echo " - Client: Not ready"
curl -f http://localhost:5678/healthz && echo " - n8n: OK" || echo " - n8n: Not ready"
curl -f http://localhost:9000/minio/health/live && echo " - MinIO: OK" || echo " - MinIO: Not ready"
curl -f http://localhost:3000 && echo " - Wiki.js: OK" || echo " - Wiki.js: Not ready"

echo ""
echo "=== Deployment Complete ==="
echo "Services are accessible at:"
echo "  • Client:   http://*************:80"
echo "  • n8n:      http://*************:5678"
echo "  • MinIO:    http://*************:9001"
echo "  • Wiki.js:  http://*************:3000"
DEPLOY_SCRIPT

    # Copy script to container and execute
    log_info "Copying deployment script to container..."
    ssh <EMAIL> "scp /tmp/container_deploy.sh nuc2:/tmp/"
    ssh <EMAIL> "ssh nuc2 'pct push ${CONTAINER_ID} /tmp/container_deploy.sh /tmp/container_deploy.sh'"
    ssh <EMAIL> "ssh nuc2 'pct exec ${CONTAINER_ID} -- chmod +x /tmp/container_deploy.sh'"
    
    log_info "Executing deployment inside container..."
    ssh <EMAIL> "ssh nuc2 'pct exec ${CONTAINER_ID} -- /tmp/container_deploy.sh'"
    
    log_success "Deployment completed!"
    
    # Display final status
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    DEPLOYMENT COMPLETED                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    echo -e "${YELLOW}🌐 Access URLs:${NC}"
    echo "  • Client Application:   http://*************:80"
    echo "  • n8n Web Interface:    http://*************:5678"
    echo "  • MinIO Console:        http://*************:9001"
    echo "  • Wiki.js:              http://*************:3000"
    echo ""
    echo -e "${YELLOW}🔧 Next Steps:${NC}"
    echo "  1. Update reverse proxy to point robo.stargety.com to *************"
    echo "  2. Import n8n workflows"
    echo "  3. Configure API credentials"
    echo "  4. Test the system"
}

# Check container status
check_status() {
    log_info "Checking container status..."
    ssh <EMAIL> "pvesh get /nodes/nuc2/lxc/200/status/current"
}

# Check running services
check_services() {
    log_info "Checking running services in container..."
    ssh <EMAIL> "ssh nuc2 'pct exec ${CONTAINER_ID} -- docker ps'" || echo "Docker not running or not installed"
}

# Main execution
case "${1:-deploy}" in
    "status")
        check_status
        ;;
    "services")
        check_services
        ;;
    "deploy")
        deploy_to_container
        ;;
    *)
        echo "Usage: $0 [deploy|status|services]"
        echo "  deploy    - Deploy ROBO-RESEARCHER-2000 to container 200"
        echo "  status    - Check container status"
        echo "  services  - Check running services"
        exit 1
        ;;
esac
