# ROBO-RESEARCHER-2000 Single Container
# Combines n8n, PostgreSQL, MinIO, and Redis into one container
FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Set timezone
ENV TZ=America/Mexico_City

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # System utilities
    curl \
    wget \
    gnupg \
    lsb-release \
    ca-certificates \
    software-properties-common \
    supervisor \
    sudo \
    # PostgreSQL
    postgresql-14 \
    postgresql-client-14 \
    postgresql-contrib-14 \
    # Redis
    redis-server \
    # Node.js dependencies (will be replaced with newer version)
    # nodejs \
    # npm \
    # Python for scripts
    python3 \
    python3-pip \
    # MinIO dependencies
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 18 using NodeSource repository
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Install n8n globally
RUN npm install -g n8n@1.63.4

# Install MinIO
RUN wget https://dl.min.io/server/minio/release/linux-amd64/minio -O /usr/local/bin/minio \
    && chmod +x /usr/local/bin/minio

# Install MinIO Client
RUN wget https://dl.min.io/client/mc/release/linux-amd64/mc -O /usr/local/bin/mc \
    && chmod +x /usr/local/bin/mc

# Create application user
RUN useradd -m -s /bin/bash robo-researcher \
    && usermod -aG sudo robo-researcher

# Create directory structure
RUN mkdir -p \
    /opt/robo-researcher \
    /opt/robo-researcher/data \
    /opt/robo-researcher/data/postgres \
    /opt/robo-researcher/data/minio \
    /opt/robo-researcher/data/n8n \
    /opt/robo-researcher/data/redis \
    /opt/robo-researcher/workflows \
    /opt/robo-researcher/scripts \
    /opt/robo-researcher/config \
    /var/log/supervisor

# Set up PostgreSQL
RUN service postgresql start \
    && sudo -u postgres createuser -s robo-researcher \
    && sudo -u postgres createdb -O robo-researcher n8n \
    && sudo -u postgres psql -c "ALTER USER \"robo-researcher\" PASSWORD 'robo-researcher-2000';" \
    && service postgresql stop

# Configure PostgreSQL
RUN echo "host all all 127.0.0.1/32 md5" >> /etc/postgresql/14/main/pg_hba.conf \
    && echo "listen_addresses = 'localhost'" >> /etc/postgresql/14/main/postgresql.conf \
    && echo "port = 5432" >> /etc/postgresql/14/main/postgresql.conf

# Configure Redis
RUN sed -i 's/^daemonize yes/daemonize no/' /etc/redis/redis.conf \
    && sed -i 's/^bind 127.0.0.1 ::1/bind 127.0.0.1/' /etc/redis/redis.conf

# Copy application files
COPY workflows/ /opt/robo-researcher/workflows/
COPY scripts/ /opt/robo-researcher/scripts/
COPY data/ /opt/robo-researcher/data/

# Install Python dependencies
COPY requirements.txt /opt/robo-researcher/
RUN pip3 install -r /opt/robo-researcher/requirements.txt

# Create n8n configuration directory
RUN mkdir -p /home/<USER>/.n8n \
    && chown -R robo-researcher:robo-researcher /home/<USER>

# Copy supervisor configuration
COPY docker/supervisor.conf /etc/supervisor/conf.d/robo-researcher.conf

# Copy startup script
COPY docker/entrypoint.sh /opt/robo-researcher/entrypoint.sh
RUN chmod +x /opt/robo-researcher/entrypoint.sh

# Copy configuration templates
COPY docker/config/ /opt/robo-researcher/config/

# Make scripts executable
RUN chmod +x /opt/robo-researcher/config/*.sh

# Create data persistence volumes
VOLUME ["/opt/robo-researcher/data", "/var/lib/postgresql/14/main", "/home/<USER>/.n8n"]

# Set ownership
RUN chown -R robo-researcher:robo-researcher /opt/robo-researcher

# Expose ports
# 5678: n8n web interface
# 9001: MinIO console (9000 is internal only)
EXPOSE 5678 9001

# Comprehensive health check
HEALTHCHECK --interval=30s --timeout=15s --start-period=120s --retries=5 \
    CMD /opt/robo-researcher/config/healthcheck.sh

# Set working directory
WORKDIR /opt/robo-researcher

# Use entrypoint script
ENTRYPOINT ["/opt/robo-researcher/entrypoint.sh"]
