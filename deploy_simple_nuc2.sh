#!/bin/bash

# ROBO-RESEARCHER-2000 Simple Deployment to nuc2 Container 200
# This script uses a simple approach to deploy to the container

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ROBO-RESEARCHER-2000                      ║"
    echo "║              Simple nuc2 Container Deployment                ║"
    echo "║                                                              ║"
    echo "║  Target: Container 200 (robo-researcher) on nuc2            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

deploy_to_container() {
    print_banner
    
    log_info "Starting simple deployment to nuc2 container 200..."
    
    # Create a comprehensive deployment script
    cat > /tmp/deploy_robo.sh << 'DEPLOY_EOF'
#!/bin/bash
set -e

echo "=== ROBO-RESEARCHER-2000 Container Deployment Starting ==="

# Update system
echo "Updating system packages..."
apt update
apt upgrade -y

# Install Docker and dependencies
echo "Installing Docker and dependencies..."
apt install -y docker.io docker-compose git curl wget nano htop python3 python3-pip

# Enable Docker
echo "Enabling Docker service..."
systemctl enable docker
systemctl start docker

# Verify Docker is running
echo "Verifying Docker installation..."
docker --version
docker-compose --version

# Clone repository
echo "Cloning ROBO-RESEARCHER-2000 repository..."
cd /opt
if [ -d "robo-researcher-2000" ]; then
    rm -rf robo-researcher-2000
fi
git clone https://github.com/c42705/robo-researcher-2000.git

# Create environment file
echo "Creating environment configuration..."
cd /opt/robo-researcher-2000
cat > .env << 'ENV_EOF'
# Core Database Configuration
POSTGRES_DB=robo_researcher
POSTGRES_USER=robo_user
POSTGRES_PASSWORD=robo_pass_2000

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000
N8N_HOST=robo.stargety.com
N8N_PROTOCOL=https
N8N_SECURE_COOKIE=false

# MinIO Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin

# Wiki.js Configuration
WIKIJS_DB_PASSWORD=wikijs_pass_2000

# Email Configuration
SMTP_HOST=mail.stargety.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# API Configuration
OPENROUTER_API_KEY=your_openrouter_key

# Client Configuration
N8N_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/robo-researcher
N8N_TEST_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/test-robo-researcher
API_BASE_URL=http://localhost:5678
NODE_ENV=production
ENV_EOF

# Install Python dependencies if available
echo "Installing Python dependencies..."
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
fi

# Deploy with Docker Compose
echo "Deploying Docker containers..."
docker-compose -f infrastructure/docker-compose.yml up -d --build

# Wait for services to start
echo "Waiting for services to start..."
sleep 90

# Check deployment
echo "Checking deployment status..."
docker ps

echo ""
echo "=== Service Health Checks ==="
curl -f http://localhost:80/health && echo "✅ Client: OK" || echo "❌ Client: Not ready"
curl -f http://localhost:5678/healthz && echo "✅ n8n: OK" || echo "❌ n8n: Not ready"
curl -f http://localhost:9000/minio/health/live && echo "✅ MinIO: OK" || echo "❌ MinIO: Not ready"
curl -f http://localhost:3000 && echo "✅ Wiki.js: OK" || echo "❌ Wiki.js: Not ready"

echo ""
echo "🎉 DEPLOYMENT COMPLETED!"
echo "Services accessible at:"
echo "  • Client:   http://*************:80"
echo "  • n8n:      http://*************:5678"
echo "  • MinIO:    http://*************:9001"
echo "  • Wiki.js:  http://*************:3000"
echo ""
echo "Next steps:"
echo "1. Update reverse proxy to point robo.stargety.com to *************"
echo "2. Import n8n workflows"
echo "3. Configure API credentials"
DEPLOY_EOF

    # Execute the deployment
    log_info "Executing deployment commands..."
    
    # Connect to proxy server and execute deployment in container 200 on nuc2
    ssh <EMAIL> << 'SSH_EOF'
# First, copy the script to nuc2
cat > /tmp/deploy_robo.sh << 'SCRIPT_EOF'
#!/bin/bash
set -e

echo "=== ROBO-RESEARCHER-2000 Container Deployment Starting ==="

# Update system
echo "Updating system packages..."
apt update
apt upgrade -y

# Install Docker and dependencies
echo "Installing Docker and dependencies..."
apt install -y docker.io docker-compose git curl wget nano htop python3 python3-pip

# Enable Docker
echo "Enabling Docker service..."
systemctl enable docker
systemctl start docker

# Verify Docker is running
echo "Verifying Docker installation..."
docker --version
docker-compose --version

# Clone repository
echo "Cloning ROBO-RESEARCHER-2000 repository..."
cd /opt
if [ -d "robo-researcher-2000" ]; then
    rm -rf robo-researcher-2000
fi
git clone https://github.com/c42705/robo-researcher-2000.git

# Create environment file
echo "Creating environment configuration..."
cd /opt/robo-researcher-2000
cat > .env << 'ENV_EOF'
# Core Database Configuration
POSTGRES_DB=robo_researcher
POSTGRES_USER=robo_user
POSTGRES_PASSWORD=robo_pass_2000

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000
N8N_HOST=robo.stargety.com
N8N_PROTOCOL=https
N8N_SECURE_COOKIE=false

# MinIO Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin

# Wiki.js Configuration
WIKIJS_DB_PASSWORD=wikijs_pass_2000

# Email Configuration
SMTP_HOST=mail.stargety.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# API Configuration
OPENROUTER_API_KEY=your_openrouter_key

# Client Configuration
N8N_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/robo-researcher
N8N_TEST_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/test-robo-researcher
API_BASE_URL=http://localhost:5678
NODE_ENV=production
ENV_EOF

# Install Python dependencies if available
echo "Installing Python dependencies..."
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
fi

# Deploy with Docker Compose
echo "Deploying Docker containers..."
docker-compose -f infrastructure/docker-compose.yml up -d --build

# Wait for services to start
echo "Waiting for services to start..."
sleep 90

# Check deployment
echo "Checking deployment status..."
docker ps

echo ""
echo "=== Service Health Checks ==="
curl -f http://localhost:80/health && echo "✅ Client: OK" || echo "❌ Client: Not ready"
curl -f http://localhost:5678/healthz && echo "✅ n8n: OK" || echo "❌ n8n: Not ready"
curl -f http://localhost:9000/minio/health/live && echo "✅ MinIO: OK" || echo "❌ MinIO: Not ready"
curl -f http://localhost:3000 && echo "✅ Wiki.js: OK" || echo "❌ Wiki.js: Not ready"

echo ""
echo "🎉 DEPLOYMENT COMPLETED!"
echo "Services accessible at:"
echo "  • Client:   http://*************:80"
echo "  • n8n:      http://*************:5678"
echo "  • MinIO:    http://*************:9001"
echo "  • Wiki.js:  http://*************:3000"
SCRIPT_EOF

# Copy script to nuc2 and execute in container
scp /tmp/deploy_robo.sh *************:/tmp/
ssh ************* "pct push 200 /tmp/deploy_robo.sh /tmp/deploy_robo.sh"
ssh ************* "pct exec 200 -- chmod +x /tmp/deploy_robo.sh"
ssh ************* "pct exec 200 -- /tmp/deploy_robo.sh"
SSH_EOF

    log_success "Deployment completed!"
    
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    DEPLOYMENT COMPLETED                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Main execution
deploy_to_container
