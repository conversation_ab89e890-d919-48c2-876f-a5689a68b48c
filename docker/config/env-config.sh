#!/bin/bash

# Environment Configuration Script for ROBO-RESEARCHER-2000
# Manages environment variables and service configuration

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to validate required environment variables
validate_environment() {
    log_info "Validating environment configuration..."

    local warnings=0

    # Check for OpenRouter API key
    if [ -z "$OPENROUTER_API_KEY" ]; then
        log_warning "OPENROUTER_API_KEY not set - AI functionality will be limited"
        warnings=$((warnings + 1))
    else
        log_success "OpenRouter API key configured"
    fi

    # Check for SMTP configuration
    if [ -z "$SMTP_HOST" ] || [ -z "$SMTP_USER" ] || [ -z "$SMTP_PASSWORD" ]; then
        log_warning "SMTP configuration incomplete - email notifications disabled"
        log_info "  Required: SMTP_HOST, SMTP_USER, SMTP_PASSWORD"
        log_info "  Optional: SMTP_PORT (default: 587)"
        warnings=$((warnings + 1))
    else
        log_success "SMTP configuration complete"
    fi

    # Set default values for optional variables
    export SMTP_PORT="${SMTP_PORT:-587}"
    export GENERIC_TIMEZONE="${GENERIC_TIMEZONE:-America/Mexico_City}"
    export N8N_LOG_LEVEL="${N8N_LOG_LEVEL:-info}"

    if [ $warnings -gt 0 ]; then
        log_warning "$warnings configuration warnings found"
        log_info "Container will start with limited functionality"
        log_info "You can add missing environment variables and restart the container"
    else
        log_success "All environment variables configured correctly"
    fi
}

# Function to generate n8n environment file
generate_n8n_env() {
    log_info "Generating n8n environment configuration..."

    local n8n_env_file="/home/<USER>/.n8n/.env"

    cat > "$n8n_env_file" <<EOF
# n8n Environment Configuration - Auto-generated
# Internal service connections (pre-configured)

# Database Configuration
DB_TYPE=postgresdb
DB_POSTGRESDB_HOST=localhost
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=robo-researcher
DB_POSTGRESDB_PASSWORD=robo-researcher-2000

# n8n Core Settings
N8N_HOST=0.0.0.0
N8N_PORT=5678
N8N_PROTOCOL=http
N8N_SECURE_COOKIE=false
WEBHOOK_URL=http://localhost:5678
GENERIC_TIMEZONE=${GENERIC_TIMEZONE}
N8N_LOG_LEVEL=${N8N_LOG_LEVEL}
N8N_METRICS=true
N8N_DIAGNOSTICS_ENABLED=true

# Authentication
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000

# User API Keys (if provided)
EOF

    # Add OpenRouter API key if provided
    if [ ! -z "$OPENROUTER_API_KEY" ]; then
        echo "OPENROUTER_API_KEY=${OPENROUTER_API_KEY}" >> "$n8n_env_file"
    fi

    # Add SMTP configuration if provided
    if [ ! -z "$SMTP_HOST" ]; then
        cat >> "$n8n_env_file" <<EOF
SMTP_HOST=${SMTP_HOST}
SMTP_PORT=${SMTP_PORT}
SMTP_USER=${SMTP_USER}
SMTP_PASSWORD=${SMTP_PASSWORD}
EOF
    fi

    # Set ownership
    chown robo-researcher:robo-researcher "$n8n_env_file"

    log_success "n8n environment file generated"
}

# Function to display configuration summary
display_config_summary() {
    log_info "=== ROBO-RESEARCHER-2000 Configuration Summary ==="
    echo ""
    echo "🔧 Internal Services (Pre-configured):"
    echo "  • PostgreSQL: localhost:5432 (database: n8n)"
    echo "  • Redis: localhost:6379"
    echo "  • MinIO: localhost:9000 (console: :9001)"
    echo "  • n8n: localhost:5678"
    echo ""
    echo "🔐 Authentication:"
    echo "  • n8n: admin / robo-researcher-2000"
    echo "  • MinIO: minioadmin / minioadmin"
    echo ""
    echo "🌐 External APIs:"
    if [ ! -z "$OPENROUTER_API_KEY" ]; then
        echo "  • OpenRouter: ✅ Configured"
    else
        echo "  • OpenRouter: ❌ Not configured"
    fi

    if [ ! -z "$SMTP_HOST" ]; then
        echo "  • SMTP Email: ✅ Configured ($SMTP_HOST)"
    else
        echo "  • SMTP Email: ❌ Not configured"
    fi
    echo ""
    echo "⚙️  Settings:"
    echo "  • Timezone: ${GENERIC_TIMEZONE}"
    echo "  • Log Level: ${N8N_LOG_LEVEL}"
    echo ""
}

# Main function
main() {
    log_info "🔧 Configuring ROBO-RESEARCHER-2000 environment..."

    # Validate environment variables
    validate_environment

    # Generate configuration files
    generate_n8n_env

    # Display summary
    display_config_summary

    log_success "Environment configuration completed!"
}

# Run main function if script is executed directly
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi