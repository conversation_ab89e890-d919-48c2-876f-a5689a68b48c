#!/bin/bash

# ROBO-RESEARCHER-2000 Single Container Entrypoint
# Manages startup of all services in the correct order

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to wait for service
wait_for_service() {
    local service_name=$1
    local check_command=$2
    local max_attempts=30
    local attempt=1
    
    log_info "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if eval "$check_command" >/dev/null 2>&1; then
            log_success "$service_name is ready!"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "$service_name failed to start after $max_attempts attempts"
    return 1
}

# Function to initialize PostgreSQL
init_postgresql() {
    log_info "Initializing PostgreSQL..."
    
    # Ensure PostgreSQL data directory exists and has correct permissions
    mkdir -p /var/lib/postgresql/14/main
    chown -R postgres:postgres /var/lib/postgresql/14/main
    chmod 700 /var/lib/postgresql/14/main
    
    # Initialize database if not exists
    if [ ! -f /var/lib/postgresql/14/main/PG_VERSION ]; then
        log_info "Initializing PostgreSQL database cluster..."
        sudo -u postgres /usr/lib/postgresql/14/bin/initdb -D /var/lib/postgresql/14/main
        
        # Start PostgreSQL temporarily to create database and user
        sudo -u postgres /usr/lib/postgresql/14/bin/pg_ctl -D /var/lib/postgresql/14/main -l /var/log/postgresql.log start
        
        # Wait for PostgreSQL to start
        sleep 5
        
        # Create database and user
        sudo -u postgres createdb n8n
        sudo -u postgres psql -c "CREATE USER \"robo-researcher\" WITH PASSWORD 'robo-researcher-2000';"
        sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE n8n TO \"robo-researcher\";"
        sudo -u postgres psql -c "ALTER USER \"robo-researcher\" CREATEDB;"
        
        # Stop PostgreSQL
        sudo -u postgres /usr/lib/postgresql/14/bin/pg_ctl -D /var/lib/postgresql/14/main stop
        
        log_success "PostgreSQL initialized successfully"
    else
        log_info "PostgreSQL already initialized"
    fi
}

# Function to initialize MinIO
init_minio() {
    log_info "Initializing MinIO..."
    
    # Create MinIO data directory
    mkdir -p /opt/robo-researcher/data/minio
    chown -R robo-researcher:robo-researcher /opt/robo-researcher/data/minio
    
    log_success "MinIO initialized successfully"
}

# Function to initialize n8n
init_n8n() {
    log_info "Initializing n8n..."
    
    # Create n8n directories
    mkdir -p /home/<USER>/.n8n
    mkdir -p /home/<USER>/.n8n/workflows
    
    # Copy workflows if they exist
    if [ -d "/opt/robo-researcher/workflows" ]; then
        cp -r /opt/robo-researcher/workflows/* /home/<USER>/.n8n/workflows/ 2>/dev/null || true
    fi
    
    # Set ownership
    chown -R robo-researcher:robo-researcher /home/<USER>/.n8n
    
    log_success "n8n initialized successfully"
}

# Function to configure services based on environment variables
configure_services() {
    log_info "Configuring services with environment variables..."
    
    # Update n8n environment variables if API keys are provided
    if [ ! -z "$OPENROUTER_API_KEY" ]; then
        log_info "OpenRouter API key detected"
        export N8N_OPENROUTER_API_KEY="$OPENROUTER_API_KEY"
    fi
    
    if [ ! -z "$SMTP_HOST" ] && [ ! -z "$SMTP_USER" ] && [ ! -z "$SMTP_PASSWORD" ]; then
        log_info "SMTP configuration detected"
        export N8N_SMTP_HOST="$SMTP_HOST"
        export N8N_SMTP_USER="$SMTP_USER"
        export N8N_SMTP_PASSWORD="$SMTP_PASSWORD"
        export N8N_SMTP_PORT="${SMTP_PORT:-587}"
    fi
    
    log_success "Service configuration completed"
}

# Function to setup MinIO buckets
setup_minio_buckets() {
    log_info "Setting up MinIO buckets..."
    
    # Wait for MinIO to be ready
    wait_for_service "MinIO" "curl -f http://localhost:9000/minio/health/live"
    
    # Configure MinIO client
    /usr/local/bin/mc alias set local http://localhost:9000 minioadmin minioadmin
    
    # Create buckets
    /usr/local/bin/mc mb local/robo-researcher-data --ignore-existing
    /usr/local/bin/mc mb local/transcripts --ignore-existing
    /usr/local/bin/mc mb local/results --ignore-existing
    /usr/local/bin/mc mb local/presentations --ignore-existing
    /usr/local/bin/mc mb local/backups --ignore-existing
    
    # Set bucket policies
    /usr/local/bin/mc anonymous set download local/results
    
    log_success "MinIO buckets configured"
}

# Function to handle graceful shutdown
graceful_shutdown() {
    log_info "Received shutdown signal, stopping services gracefully..."
    
    # Stop supervisor and all services
    supervisorctl stop all
    killall supervisord
    
    log_success "All services stopped"
    exit 0
}

# Set up signal handlers
trap graceful_shutdown SIGTERM SIGINT

# Main startup sequence
main() {
    log_info "🤖 Starting ROBO-RESEARCHER-2000 Single Container..."
    
    # Initialize services
    init_postgresql
    init_minio
    init_n8n
    
    # Configure services
    configure_services

    # Run environment configuration
    log_info "Running environment configuration..."
    source /opt/robo-researcher/config/env-config.sh

    # Start supervisor (which starts all services)
    log_info "Starting all services with supervisor..."
    /usr/bin/supervisord -c /etc/supervisor/conf.d/robo-researcher.conf &
    
    # Wait for services to be ready
    wait_for_service "PostgreSQL" "pg_isready -h localhost -U robo-researcher -d n8n"
    wait_for_service "Redis" "redis-cli -h localhost ping"
    wait_for_service "MinIO" "curl -f http://localhost:9000/minio/health/live"
    
    # Setup MinIO buckets
    setup_minio_buckets
    
    # Wait for n8n
    wait_for_service "n8n" "curl -f http://localhost:5678/healthz"

    # Setup n8n credentials in background
    log_info "Setting up n8n credentials..."
    /opt/robo-researcher/config/setup-n8n-credentials.sh &

    log_success "🎉 ROBO-RESEARCHER-2000 is ready!"
    log_info "Access URLs:"
    log_info "  • n8n Workflow Engine: http://localhost:5678 (admin / robo-researcher-2000)"
    log_info "  • MinIO Console: http://localhost:9001 (minioadmin / minioadmin)"

    # Keep container running
    wait
}

# Run main function
main "$@"
