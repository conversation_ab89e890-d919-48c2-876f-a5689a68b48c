[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

# PostgreSQL Database
[program:postgresql]
command=/usr/lib/postgresql/14/bin/postgres -D /var/lib/postgresql/14/main -c config_file=/etc/postgresql/14/main/postgresql.conf
user=postgres
autostart=true
autorestart=true
priority=100
stdout_logfile=/var/log/supervisor/postgresql.log
stderr_logfile=/var/log/supervisor/postgresql_error.log
environment=PGDATA="/var/lib/postgresql/14/main"

# Redis Cache
[program:redis]
command=/usr/bin/redis-server /etc/redis/redis.conf
user=redis
autostart=true
autorestart=true
priority=200
stdout_logfile=/var/log/supervisor/redis.log
stderr_logfile=/var/log/supervisor/redis_error.log

# MinIO Storage
[program:minio]
command=/usr/local/bin/minio server /opt/robo-researcher/data/minio --console-address ":9001"
user=robo-researcher
autostart=true
autorestart=true
priority=300
stdout_logfile=/var/log/supervisor/minio.log
stderr_logfile=/var/log/supervisor/minio_error.log
environment=MINIO_ROOT_USER="minioadmin",MINIO_ROOT_PASSWORD="minioadmin",MINIO_BROWSER_REDIRECT_URL="http://localhost:9001"

# n8n Workflow Engine
[program:n8n]
command=/usr/bin/node /usr/local/bin/n8n start
user=robo-researcher
autostart=true
autorestart=true
priority=400
stdout_logfile=/var/log/supervisor/n8n.log
stderr_logfile=/var/log/supervisor/n8n_error.log
environment=HOME="/home/<USER>",
    N8N_HOST="0.0.0.0",
    N8N_PORT="5678",
    N8N_PROTOCOL="http",
    N8N_SECURE_COOKIE="false",
    N8N_BASIC_AUTH_ACTIVE="true",
    N8N_BASIC_AUTH_USER="admin",
    N8N_BASIC_AUTH_PASSWORD="robo-researcher-2000",
    WEBHOOK_URL="http://localhost:5678",
    GENERIC_TIMEZONE="America/Mexico_City",
    N8N_LOG_LEVEL="info",
    N8N_METRICS="true",
    N8N_DIAGNOSTICS_ENABLED="true",
    DB_TYPE="postgresdb",
    DB_POSTGRESDB_HOST="localhost",
    DB_POSTGRESDB_PORT="5432",
    DB_POSTGRESDB_DATABASE="n8n",
    DB_POSTGRESDB_USER="robo-researcher",
    DB_POSTGRESDB_PASSWORD="robo-researcher-2000",
    N8N_USER_FOLDER="/home/<USER>/.n8n"
