open router (free)



--------
"""
{
  "mcpServers": {
    "n8n-mcp": {
      "command": "docker",
      "args": [
        "run",
        "-i",
        "--rm",
        "-e", "MCP_MODE=stdio",
        "-e", "LOG_LEVEL=error",
        "-e", "DISABLE_CONSOLE_OUTPUT=true",
        "-e", "N8N_API_URL=https://vps.stargety.com/",
        "-e", "N8N_API_KEY=your-api-here",
        "ghcr.io/c<PERSON><PERSON><PERSON>/n8n-mcp:latest"
      ]
    }
  }
}
"""