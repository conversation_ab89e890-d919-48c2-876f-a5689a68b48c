#!/bin/bash

# ROBO-RESEARCHER-2000 Local Deployment Script
# This script sets up the complete system locally using Docker

set -e

echo "🤖 ROBO-RESEARCHER-2000 Local Deployment"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p infrastructure/n8n/workflows
    mkdir -p infrastructure/n8n/credentials
    mkdir -p infrastructure/wikijs
    mkdir -p data/uploads
    mkdir -p data/outputs
    
    print_success "Directories created"
}

# Create environment file if it doesn't exist
create_env_file() {
    if [ ! -f .env ]; then
        print_status "Creating .env file..."
        cat > .env << EOF
# ROBO-RESEARCHER-2000 Environment Configuration

# n8n Configuration
N8N_HOST=localhost
N8N_PROTOCOL=http
N8N_WEBHOOK_URL=http://localhost:5678

# Database Configuration
POSTGRES_DB=n8n
POSTGRES_USER=n8n
POSTGRES_PASSWORD=n8n_password

# MinIO Configuration
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# API Keys (REQUIRED - Please update these)
OPENROUTER_API_KEY=your_openrouter_api_key_here
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# Optional Configuration
GENERIC_TIMEZONE=America/Mexico_City
N8N_LOG_LEVEL=info
EOF
        print_warning "Created .env file. Please update API keys before starting services!"
    else
        print_status ".env file already exists"
    fi
}

# Copy workflows to the right location
copy_workflows() {
    print_status "Copying workflows..."
    
    if [ -d "workflows" ]; then
        cp -r workflows/* infrastructure/n8n/workflows/ 2>/dev/null || true
        print_success "Workflows copied"
    else
        print_warning "No workflows directory found"
    fi
}

# Start services
start_services() {
    print_status "Starting Docker services..."

    # Determine which docker compose command to use
    if command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    else
        COMPOSE_CMD="docker compose"
    fi

    # Pull latest images
    print_status "Pulling Docker images..."
    $COMPOSE_CMD -f docker-compose.local.yml pull

    # Start services
    print_status "Starting services..."
    $COMPOSE_CMD -f docker-compose.local.yml up -d

    print_success "Services started"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for n8n
    print_status "Waiting for n8n..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        print_warning "n8n health check timeout, but continuing..."
    else
        print_success "n8n is ready"
    fi
    
    # Wait for MinIO
    print_status "Waiting for MinIO..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:9000/minio/health/live > /dev/null 2>&1; then
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        print_warning "MinIO health check timeout, but continuing..."
    else
        print_success "MinIO is ready"
    fi
}

# Display service information
show_service_info() {
    echo ""
    print_success "🎉 ROBO-RESEARCHER-2000 is now running locally!"
    echo ""
    echo "📋 Service URLs:"
    echo "  • n8n (Workflow Engine):     http://localhost:5678"
    echo "  • MinIO (Storage):           http://localhost:9001"
    echo "  • Wiki.js (Documentation):  http://localhost:3000"
    echo "  • Client App:               http://localhost:80"
    echo ""
    echo "🔐 Default Credentials:"
    echo "  • n8n:     admin / robo-researcher-2000"
    echo "  • MinIO:   minioadmin / minioadmin"
    echo ""
    echo "📝 Next Steps:"
    echo "  1. Update API keys in .env file"
    echo "  2. Import workflows in n8n"
    echo "  3. Configure credentials in n8n"
    echo "  4. Test the system with sample data"
    echo ""
    echo "🔧 Management Commands:"
    echo "  • Stop:    docker compose -f docker-compose.local.yml down"
    echo "  • Restart: docker compose -f docker-compose.local.yml restart"
    echo "  • Logs:    docker compose -f docker-compose.local.yml logs -f"
    echo "  • Status:  docker compose -f docker-compose.local.yml ps"
    echo ""
}

# Main execution
main() {
    check_prerequisites
    create_directories
    create_env_file
    copy_workflows
    start_services
    wait_for_services
    show_service_info
}

# Run main function
main "$@"
