#!/bin/bash

# ROBO-RESEARCHER-2000 Final Deployment Script
# Automated deployment and validation for production environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ROBO-RESEARCHER-2000"
DOCKER_COMPOSE_FILE="infrastructure/docker-compose.yml"
ENV_FILE=".env"
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ROBO-RESEARCHER-2000                      ║"
    echo "║                  Final Deployment Script                     ║"
    echo "║                                                              ║"
    echo "║  Automated UX Research Analysis System                       ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed. Please install Python 3 first."
        exit 1
    fi
    
    # Check Git
    if ! command -v git &> /dev/null; then
        log_error "Git is not installed. Please install Git first."
        exit 1
    fi
    
    log_success "All prerequisites are installed"
}

check_environment() {
    log_info "Checking environment configuration..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "Environment file not found. Creating from template..."
        if [ -f ".env.example" ]; then
            cp .env.example $ENV_FILE
            log_info "Please edit $ENV_FILE with your configuration"
            read -p "Press Enter to continue after editing the environment file..."
        else
            log_error "No .env.example file found. Please create $ENV_FILE manually."
            exit 1
        fi
    fi
    
    # Check required environment variables
    source $ENV_FILE
    
    required_vars=("OPENROUTER_API_KEY" "SMTP_HOST" "SMTP_USER" "SMTP_PASSWORD")
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_warning "Missing required environment variables: ${missing_vars[*]}"
        log_info "The system will work with limited functionality"
    else
        log_success "All required environment variables are set"
    fi
}

backup_existing_data() {
    log_info "Creating backup of existing data..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup Docker volumes if they exist
    if docker volume ls | grep -q "robo-researcher"; then
        log_info "Backing up Docker volumes..."
        docker run --rm -v robo-researcher_postgres_data:/source -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/postgres_data.tar.gz -C /source .
        docker run --rm -v robo-researcher_minio_data:/source -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/minio_data.tar.gz -C /source .
        docker run --rm -v robo-researcher_n8n_data:/source -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/n8n_data.tar.gz -C /source .
    fi
    
    # Backup configuration files
    cp -r workflows "$BACKUP_DIR/" 2>/dev/null || true
    cp $ENV_FILE "$BACKUP_DIR/" 2>/dev/null || true
    
    log_success "Backup created in $BACKUP_DIR"
}

deploy_infrastructure() {
    log_info "Deploying infrastructure..."
    
    # Navigate to infrastructure directory
    cd infrastructure
    
    # Stop existing containers
    log_info "Stopping existing containers..."
    docker-compose down || true
    
    # Pull latest images
    log_info "Pulling latest Docker images..."
    docker-compose pull
    
    # Start services
    log_info "Starting services..."
    docker-compose up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to start..."
    sleep 30
    
    cd ..
    log_success "Infrastructure deployed successfully"
}

install_python_dependencies() {
    log_info "Installing Python dependencies..."
    
    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt
        log_success "Python dependencies installed"
    else
        log_warning "No requirements.txt found, skipping Python dependencies"
    fi
}

validate_deployment() {
    log_info "Validating deployment..."
    
    # Run deployment validation
    if [ -f "tests/deployment_validator.py" ]; then
        python3 tests/deployment_validator.py
        if [ $? -eq 0 ]; then
            log_success "Deployment validation passed"
        else
            log_error "Deployment validation failed"
            return 1
        fi
    else
        log_warning "Deployment validator not found, skipping validation"
    fi
    
    # Run integration tests
    if [ -f "tests/integration_test.py" ]; then
        log_info "Running integration tests..."
        python3 tests/integration_test.py
        if [ $? -eq 0 ]; then
            log_success "Integration tests passed"
        else
            log_warning "Integration tests failed, but deployment continues"
        fi
    fi
    
    # Run final comprehensive test
    if [ -f "tests/final_comprehensive_test.py" ]; then
        log_info "Running final comprehensive test..."
        python3 tests/final_comprehensive_test.py
        if [ $? -eq 0 ]; then
            log_success "Final comprehensive test passed"
        else
            log_warning "Final comprehensive test failed, but deployment continues"
        fi
    fi
}

setup_client_application() {
    log_info "Setting up client application..."
    
    if [ -d "client" ]; then
        cd client
        
        # Start simple HTTP server for client
        log_info "Client application available at: http://localhost:8080"
        log_info "To serve the client application, run: python3 -m http.server 8080"
        
        cd ..
        log_success "Client application setup completed"
    else
        log_warning "Client directory not found"
    fi
}

import_workflows() {
    log_info "Importing n8n workflows..."
    
    # Wait for n8n to be fully ready
    log_info "Waiting for n8n to be ready..."
    sleep 60
    
    # Check if n8n is accessible
    if curl -f http://localhost:5678/healthz > /dev/null 2>&1; then
        log_success "n8n is accessible"
        log_info "Please manually import workflows from the workflows/ directory"
        log_info "1. Open http://localhost:5678"
        log_info "2. Go to Workflows → Import from file"
        log_info "3. Import workflows/test-workflow-simple.json"
        log_info "4. Import workflows/main-workflow-complete.json"
        log_info "5. Configure credentials as described in the documentation"
    else
        log_warning "n8n is not accessible yet. Please import workflows manually later."
    fi
}

print_deployment_summary() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    DEPLOYMENT COMPLETED                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    echo -e "${BLUE}🎉 ROBO-RESEARCHER-2000 has been deployed successfully!${NC}"
    echo ""
    echo -e "${YELLOW}📋 Service URLs:${NC}"
    echo "  • n8n Web Interface:    http://localhost:5678"
    echo "  • MinIO Console:        http://localhost:9001"
    echo "  • Wiki.js:              http://localhost:3000"
    echo "  • Client Application:   http://localhost:8080"
    echo ""
    echo -e "${YELLOW}🔑 Default Credentials:${NC}"
    echo "  • n8n:     admin / robo-researcher-2000"
    echo "  • MinIO:   minioadmin / minioadmin"
    echo "  • Wiki.js: <EMAIL> / robo-researcher-2000"
    echo ""
    echo -e "${YELLOW}📚 Next Steps:${NC}"
    echo "  1. Import n8n workflows from workflows/ directory"
    echo "  2. Configure API credentials in n8n"
    echo "  3. Test the system with sample data"
    echo "  4. Review documentation in docs/ directory"
    echo ""
    echo -e "${YELLOW}🔧 Management Commands:${NC}"
    echo "  • View logs:     docker-compose -f infrastructure/docker-compose.yml logs -f"
    echo "  • Stop services: docker-compose -f infrastructure/docker-compose.yml down"
    echo "  • Start services: docker-compose -f infrastructure/docker-compose.yml up -d"
    echo ""
    echo -e "${YELLOW}📖 Documentation:${NC}"
    echo "  • Setup Guide:      docs/setup-guide.md"
    echo "  • User Manual:      docs/user-manual.md"
    echo "  • API Reference:    docs/api-reference.md"
    echo "  • Deployment Guide: docs/deployment-guide.md"
    echo ""
    echo -e "${RED}⚠️  Security Notice:${NC}"
    echo "  • Change default passwords in production"
    echo "  • Configure HTTPS for external access"
    echo "  • Review security settings in documentation"
    echo ""
}

# Main deployment process
main() {
    print_banner
    
    log_info "Starting deployment of $PROJECT_NAME..."
    
    # Check if running as root (not recommended)
    if [ "$EUID" -eq 0 ]; then
        log_warning "Running as root is not recommended for security reasons"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Run deployment steps
    check_prerequisites
    check_environment
    backup_existing_data
    install_python_dependencies
    deploy_infrastructure
    validate_deployment
    setup_client_application
    import_workflows
    
    print_deployment_summary
    
    log_success "Deployment completed successfully!"
    
    # Ask if user wants to open the client application
    read -p "Would you like to start the client application now? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Starting client application..."
        cd client
        python3 -m http.server 8080 &
        CLIENT_PID=$!
        log_info "Client application started with PID: $CLIENT_PID"
        log_info "Access the application at: http://localhost:8080"
        log_info "To stop the client application, run: kill $CLIENT_PID"
    fi
}

# Handle script interruption
trap 'log_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
