# ROBO-RESEARCHER-2000 Deployment Assessment for nuc1

## 🔍 System Analysis

### Current System Status (nuc1 @ prox.stargety.in)
- **IP Address**: ************* (not ************* as expected)
- **Domain**: robo.stargety.com (points to ************* - needs update)
- **OS**: Debian GNU/Linux (Proxmox VE 6.8.12-9-pve)
- **CPU**: Intel Core i5-5250U @ 1.60GHz (4 cores)
- **RAM**: 7.6GB total, **6.9GB used, only 795MB available**
- **Storage**: 37GB total, 16GB used, 20GB available
- **Swap**: 7.6GB total, 3.9GB used

### Current Running Services
- **Container 100**: bookstack (documentation system)
- **Container 101**: nginxproxymanager (reverse proxy)
- **Docker**: Not installed on host

## ⚠️ Critical Assessment

### 🔴 **DEPLOYMENT NOT RECOMMENDED ON CURRENT SYSTEM**

**Reasons:**
1. **Insufficient Memory**: Only 795MB available, ROBO-RESEARCHER-2000 requires minimum 2GB
2. **High System Load**: 90%+ memory utilization indicates overloaded system
3. **Swap Usage**: 3.9GB swap usage indicates memory pressure
4. **Resource Conflicts**: Existing services consuming significant resources

### Memory Requirements Analysis
ROBO-RESEARCHER-2000 minimum requirements:
- **n8n**: 512MB RAM
- **PostgreSQL**: 256MB RAM
- **MinIO**: 256MB RAM
- **Wiki.js**: 512MB RAM
- **System overhead**: 512MB RAM
- **Total minimum**: ~2GB RAM

**Current available**: 795MB (insufficient)

## 🎯 Recommended Solutions

### Option 1: Resource Optimization (Preferred)
1. **Stop non-essential containers temporarily**
2. **Optimize existing services**
3. **Deploy minimal ROBO-RESEARCHER-2000 configuration**

### Option 2: Alternative Deployment Location
1. **Use different NUC instance** at prox.stargety.in
2. **Deploy on cloud infrastructure** (AWS, GCP, Azure)
3. **Use local development environment** for testing

### Option 3: System Upgrade
1. **Add more RAM** to nuc1 (if possible)
2. **Migrate existing services** to other instances
3. **Optimize Proxmox configuration**

## 🛠️ Updated Deployment Plan (With Client Container)

### Phase 1: System Preparation
```bash
# Check available instances
pct list
qm list

# Stop non-essential services (if approved)
pct stop 100  # bookstack (if not critical)

# Install Docker
apt update
apt install -y docker.io docker-compose
systemctl enable docker
systemctl start docker
```

### Phase 2: Repository Setup
```bash
# Clone repository
git clone https://github.com/c42705/robo-researcher-2000.git
cd robo-researcher-2000

# Create environment configuration
cat > .env << EOF
# Core Database Configuration
POSTGRES_DB=robo_researcher
POSTGRES_USER=robo_user
POSTGRES_PASSWORD=robo_pass_2000

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000
N8N_HOST=robo.stargety.com
N8N_PROTOCOL=https
N8N_SECURE_COOKIE=false

# MinIO Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# Wiki.js Configuration
WIKIJS_DB_PASSWORD=wikijs_pass_2000

# Email Configuration
SMTP_HOST=mail.stargety.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# API Configuration
OPENROUTER_API_KEY=your_openrouter_key

# Client Configuration
N8N_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/robo-researcher
N8N_TEST_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/test-robo-researcher
API_BASE_URL=http://localhost:5678
EOF
```

### Phase 3: Deploy with Client Container
```bash
# Build and deploy all services including client
docker-compose -f infrastructure/docker-compose.yml up -d --build

# Verify all containers are running
docker ps

# Check logs for any issues
docker-compose -f infrastructure/docker-compose.yml logs -f
```

### Phase 4: Reverse Proxy Configuration
```bash
# Configure nginxproxymanager (Container 101)
# Add proxy hosts for:
# - robo.stargety.com -> *************:80 (Client Application)
# - api.robo.stargety.com -> *************:5678 (n8n API)
# - minio.robo.stargety.com -> *************:9001 (MinIO Console)
# - wiki.robo.stargety.com -> *************:3000 (Wiki.js)

# Update DNS if needed
# robo.stargety.com should point to ************* (currently points to .175)
```

### Phase 5: Service Verification
```bash
# Check all services are accessible
curl -f http://localhost:80/health          # Client health check
curl -f http://localhost:5678/healthz       # n8n health check
curl -f http://localhost:9000/minio/health/live  # MinIO health check
curl -f http://localhost:3000               # Wiki.js check

# Test client application
curl -f http://localhost:80/                # Main client page
curl -f http://localhost:80/api/healthz     # API proxy check
```

## 📊 Resource Monitoring

### Commands to Monitor System Health
```bash
# Memory usage
free -h
watch -n 5 'free -h'

# Container resource usage
docker stats

# System load
htop
iostat -x 1

# Disk usage
df -h
du -sh /var/lib/docker/
```

### Warning Thresholds
- **Memory usage > 95%**: Stop deployment
- **Swap usage > 90%**: Investigate memory leaks
- **Disk usage > 85%**: Clean up or expand storage
- **Load average > 4**: System overloaded

## 🔧 Alternative Deployment Strategies

### Strategy 1: Lightweight Configuration
```yaml
# Reduced resource limits in docker-compose.yml
services:
  postgres:
    deploy:
      resources:
        limits:
          memory: 128M
  n8n:
    deploy:
      resources:
        limits:
          memory: 256M
  minio:
    deploy:
      resources:
        limits:
          memory: 128M
  wikijs:
    deploy:
      resources:
        limits:
          memory: 256M
```

### Strategy 2: External Services
- Use external PostgreSQL (managed service)
- Use external object storage (AWS S3)
- Deploy only n8n and client application

### Strategy 3: Development Mode
- Single container with all services
- SQLite instead of PostgreSQL
- File storage instead of MinIO
- Simplified workflow for testing

## 🚨 Risk Assessment

### High Risks
1. **System instability** due to memory pressure
2. **Service crashes** from OOM (Out of Memory) conditions
3. **Data loss** if system becomes unresponsive
4. **Performance degradation** of existing services

### Mitigation Strategies
1. **Continuous monitoring** during deployment
2. **Rollback plan** ready (stop containers immediately)
3. **Backup existing data** before deployment
4. **Staged deployment** (one service at a time)

## 📋 Pre-Deployment Checklist

- [ ] Backup existing container data
- [ ] Verify DNS configuration for robo.stargety.com
- [ ] Obtain necessary API keys (OpenRouter, SMTP)
- [ ] Test minimal Docker deployment locally
- [ ] Prepare rollback procedures
- [ ] Schedule maintenance window
- [ ] Notify stakeholders of potential service impact

## 🎯 Final Recommendation

**DO NOT DEPLOY** on current nuc1 system without:
1. **Stopping bookstack container** (Container 100) to free ~1-2GB RAM
2. **Implementing resource limits** for all containers
3. **Continuous monitoring** during deployment
4. **Immediate rollback capability**

**PREFERRED ALTERNATIVE**: Deploy on a different NUC instance with adequate resources (minimum 4GB available RAM).

## 📞 Next Steps

1. **Consult with system administrator** about resource allocation
2. **Evaluate alternative NUC instances** at prox.stargety.in
3. **Consider cloud deployment** for production use
4. **Test minimal deployment** in development environment first

---

**Assessment Date**: July 17, 2025  
**Assessor**: ROBO-RESEARCHER-2000 Deployment Team  
**Status**: ⚠️ **DEPLOYMENT NOT RECOMMENDED WITHOUT RESOURCE OPTIMIZATION**
