{"name": "random Email sender", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours"}]}}, "id": "fa96b20e-a257-4d65-8b99-fe0969b5db89", "name": "Hourly Weather Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, -384], "retryOnFail": true, "maxTries": 3, "waitBetweenTries": 1000}, {"parameters": {"jsCode": "// Generate random weather question for Gemini AI\nconst weatherQuestions = [\n  'What is the current weather in Madrid, Spain? Please provide temperature, humidity, and general conditions.',\n  'How is the weather today in Tokyo, Japan? Include any weather alerts or notable conditions.',\n  'What is the temperature and weather forecast for New York City right now?',\n  'Is it raining in London today? What are the current weather conditions?',\n  'What is the weather forecast for Paris, France for the next 24 hours?',\n  'How is the weather in Sydney, Australia today? Include temperature and conditions.',\n  'What are the current weather conditions in Mexico City, Mexico?',\n  'Is there any severe weather happening in Miami, Florida right now?',\n  'What is the temperature and humidity in Berlin, Germany today?',\n  'How is the weather in São Paulo, Brazil? Any rain or storms expected?'\n];\n\nconst cities = ['Madrid', 'Tokyo', 'New York', 'London', 'Paris', 'Sydney', 'Mexico City', 'Miami', 'Berlin', 'São Paulo'];\nconst randomQuestion = weatherQuestions[Math.floor(Math.random() * weatherQuestions.length)];\nconst randomCity = cities[Math.floor(Math.random() * cities.length)];\n\nconst currentTime = new Date();\nconst timestamp = currentTime.toISOString();\nconst readableTime = currentTime.toLocaleString('en-US', {\n  timeZone: 'UTC',\n  year: 'numeric',\n  month: 'long',\n  day: 'numeric',\n  hour: '2-digit',\n  minute: '2-digit',\n  second: '2-digit'\n});\n\nreturn [{\n  json: {\n    weatherQuestion: randomQuestion,\n    selectedCity: randomCity,\n    timestamp: timestamp,\n    readableTime: readableTime,\n    requestId: `weather-${Date.now()}`\n  }\n}];"}, "id": "26aff8ed-3df1-4e96-9e31-71904282dbb5", "name": "Generate Random Weather Question", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [224, -384], "retryOnFail": true, "maxTries": 2, "waitBetweenTries": 1000}, {"parameters": {"jsCode": "// Process LLM Chain response and prepare email content\nconst input = items[0].json;\n\n// Extract the AI response from Basic LLM Chain output\nlet aiResponse = 'No response received from AI';\n\n// Basic LLM Chain outputs the response directly in the 'output' field\nif (input.output && typeof input.output === 'string' && input.output.trim() !== '') {\n  aiResponse = input.output.trim();\n} else if (input.text && typeof input.text === 'string' && input.text.trim() !== '') {\n  // Fallback: sometimes the response might be in 'text' field\n  aiResponse = input.text.trim();\n} else if (input.response && typeof input.response === 'string' && input.response.trim() !== '') {\n  // Another fallback: check 'response' field\n  aiResponse = input.response.trim();\n} else {\n  // Debug: log the actual structure received for troubleshooting\n  console.log('Received input structure:', JSON.stringify(input, null, 2));\n  console.log('Available fields:', Object.keys(input));\n  \n  // Try to find any string field that might contain the response\n  const stringFields = Object.keys(input).filter(key => \n    typeof input[key] === 'string' && input[key].trim() !== ''\n  );\n  \n  if (stringFields.length > 0) {\n    console.log('Found string fields:', stringFields);\n    // Use the first non-empty string field as fallback\n    aiResponse = input[stringFields[0]].trim();\n  } else {\n    aiResponse = 'AI response format not recognized. Please check the OpenRouter credentials and workflow configuration.';\n  }\n}\n\n// Get the original question from the previous node\nconst originalQuestion = $node['Generate Random Weather Question'].json.weatherQuestion;\nconst timestamp = $node['Generate Random Weather Question'].json.readableTime;\nconst requestId = $node['Generate Random Weather Question'].json.requestId;\n\n// Create email subject with timestamp\nconst emailSubject = `🌤️ Weather Update from Gemini AI - ${new Date().toLocaleDateString()}`;\n\n// Create HTML email body\nconst emailBody = `\n<html>\n<head>\n  <style>\n    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n    .header { background-color: #4285f4; color: white; padding: 20px; text-align: center; }\n    .content { padding: 20px; }\n    .question { background-color: #f8f9fa; padding: 15px; border-left: 4px solid #4285f4; margin: 15px 0; }\n    .response { background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 15px 0; }\n    .footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }\n  </style>\n</head>\n<body>\n  <div class=\"header\">\n    <h1>🌤️ Weather Update from Gemini AI</h1>\n    <p>Automated Weather Information Service</p>\n  </div>\n  \n  <div class=\"content\">\n    <h2>Weather Question Asked:</h2>\n    <div class=\"question\">\n      <strong>❓ Question:</strong> ${originalQuestion}\n    </div>\n    \n    <h2>Gemini AI Response:</h2>\n    <div class=\"response\">\n      ${aiResponse.replace(/\\n/g, '<br>')}\n    </div>\n    \n    <p><strong>📅 Generated on:</strong> ${timestamp} UTC</p>\n    <p><strong>🔍 Request ID:</strong> ${requestId}</p>\n  </div>\n  \n  <div class=\"footer\">\n    <p>This email was automatically generated by n8n workflow \"enviador-automatico\"</p>\n    <p>Powered by Google Gemini 2.5 Flash AI via OpenRouter • Sent via Stargety SMTP</p>\n  </div>\n</body>\n</html>\n`;\n\nreturn [{\n  json: {\n    emailSubject: emailSubject,\n    emailBody: emailBody,\n    originalQuestion: originalQuestion,\n    aiResponse: aiResponse,\n    timestamp: timestamp,\n    requestId: requestId,\n    recipient: '<EMAIL>'\n  }\n}];"}, "id": "c1c6ba0a-e0ec-418e-996c-d85a998c5ba1", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [848, -384], "retryOnFail": true, "maxTries": 2, "waitBetweenTries": 1000}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.recipient }}", "subject": "={{ $json.emailSubject }}", "html": "={{ $json.emailBody }}", "options": {"appendAttribution": false}}, "id": "3a755f1d-82f3-4fe6-833f-7e479efea729", "name": "Send Email via SMTP", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1072, -384], "retryOnFail": true, "maxTries": 3, "waitBetweenTries": 2000, "webhookId": "d595362f-5a08-4ea5-9264-d2d769c79d68", "credentials": {"smtp": {"id": "T5ctAlNzzgBjRwtZ", "name": "SMTP account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.weatherQuestion }}", "messages": {"messageValues": [{"message": "You are a helpful weather assistant. Provide detailed, accurate weather information in a conversational tone. If you don't have real-time data, provide general weather patterns and advice for the requested location. Keep your response informative but concise (2-3 paragraphs maximum)."}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [448, -384], "id": "6f38b973-1522-401b-b81b-64d126de282c", "name": "Basic LLM Chain"}, {"parameters": {"model": "google/gemini-2.5-flash-lite-preview-06-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [544, -160], "id": "3c70ccf6-e9c8-41a4-93ed-75d24fed7f7b", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "Wyihkda1ZkMGqdWT", "name": "OpenRouter account"}}}], "pinData": {}, "connections": {"Hourly Weather Trigger": {"main": [[{"node": "Generate Random Weather Question", "type": "main", "index": 0}]]}, "Generate Random Weather Question": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Send Email via SMTP", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "a26916dd-e0bd-46b7-9a72-933ec5d6c10d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "0828ed415eaadbb7067f7c2c63fc9888d18dab7916e2e53339b920682432ddca"}, "id": "X51hVVQv3LKwtd1m", "tags": []}