# ROBO-RESEARCHER-2000 Single Container Configuration
# Only essential API keys are required - all internal services are pre-configured

# =============================================================================
# REQUIRED: AI API Configuration
# =============================================================================

# OpenRouter API Key (Required for AI functionality)
# Get your key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# =============================================================================
# REQUIRED: Email Configuration (for notifications)
# =============================================================================

# SMTP Configuration for email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# Alternative SMTP providers:
# SMTP_HOST=mail.stargety.com
# SMTP_USER=<EMAIL>

# =============================================================================
# OPTIONAL: Advanced Settings
# =============================================================================

# Timezone (default: America/Mexico_City)
GENERIC_TIMEZONE=America/Mexico_City

# Log level (default: info)
# Options: error, warn, info, verbose, debug
N8N_LOG_LEVEL=info

# =============================================================================
# INTERNAL SERVICES (Pre-configured - DO NOT CHANGE)
# =============================================================================

# These are automatically configured inside the container:
# - PostgreSQL: localhost:5432 (user: robo-researcher, db: n8n)
# - Redis: localhost:6379
# - MinIO: localhost:9000 (minioadmin/minioadmin)
# - n8n: localhost:5678 (admin/robo-researcher-2000)
