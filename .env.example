# ===========================================
# ROBO-RESEARCHER-2000 Configuration
# ===========================================

# n8n Configuration
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http
N8N_WEBHOOK_URL=http://localhost:5678/webhook/robo-researcher

# MinIO Configuration
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=robo-researcher-data
MINIO_USE_SSL=false

# Wiki.js Configuration
WIKIJS_URL=http://localhost:3000
WIKIJS_API_TOKEN=your_wikijs_api_token_here

# OpenRouter AI Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=anthropic/claude-3-sonnet

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password_here
SMTP_FROM_NAME=ROBO-RESEARCHER-2000
SMTP_USE_TLS=true

# GitHub Pages Configuration
GITHUB_PAGES_URL=https://your-username.github.io/robo-researcher-2000

# Analysis Configuration
DEFAULT_LANGUAGE=es
MAX_SEGMENTS_PER_ANALYSIS=1000
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_ENTITY_EXTRACTION=true

# Security
JWT_SECRET=your_jwt_secret_here
API_RATE_LIMIT=100

# Development
DEBUG=false
LOG_LEVEL=INFO
ENVIRONMENT=production

# Docker Configuration
COMPOSE_PROJECT_NAME=robo-researcher-2000
DOCKER_NETWORK=robo-researcher-network

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
