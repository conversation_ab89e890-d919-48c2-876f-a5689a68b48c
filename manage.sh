#!/bin/bash

# ROBO-RESEARCHER-2000 Management Script
# Simple script to manage the local Docker deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Determine which docker compose command to use
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    COMPOSE_CMD="docker compose"
fi

COMPOSE_FILE="docker-compose.local.yml"

# Function to show usage
show_usage() {
    echo "🤖 ROBO-RESEARCHER-2000 Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start all services"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo "  status    Show service status"
    echo "  logs      Show service logs"
    echo "  update    Update services (pull latest images)"
    echo "  clean     Stop and remove all containers and volumes"
    echo "  health    Check service health"
    echo "  urls      Show access URLs"
    echo "  help      Show this help message"
    echo ""
}

# Function to start services
start_services() {
    print_status "Starting ROBO-RESEARCHER-2000 services..."
    $COMPOSE_CMD -f $COMPOSE_FILE up -d
    print_success "Services started!"
    show_urls
}

# Function to stop services
stop_services() {
    print_status "Stopping ROBO-RESEARCHER-2000 services..."
    $COMPOSE_CMD -f $COMPOSE_FILE down
    print_success "Services stopped!"
}

# Function to restart services
restart_services() {
    print_status "Restarting ROBO-RESEARCHER-2000 services..."
    $COMPOSE_CMD -f $COMPOSE_FILE restart
    print_success "Services restarted!"
}

# Function to show status
show_status() {
    print_status "Service status:"
    $COMPOSE_CMD -f $COMPOSE_FILE ps
}

# Function to show logs
show_logs() {
    print_status "Showing service logs (Ctrl+C to exit)..."
    $COMPOSE_CMD -f $COMPOSE_FILE logs -f
}

# Function to update services
update_services() {
    print_status "Updating ROBO-RESEARCHER-2000 services..."
    $COMPOSE_CMD -f $COMPOSE_FILE pull
    $COMPOSE_CMD -f $COMPOSE_FILE up -d
    print_success "Services updated!"
}

# Function to clean everything
clean_services() {
    print_warning "This will stop and remove all containers and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up ROBO-RESEARCHER-2000..."
        $COMPOSE_CMD -f $COMPOSE_FILE down -v --remove-orphans
        print_success "Cleanup complete!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to check health
check_health() {
    print_status "Checking service health..."
    
    # Check n8n
    if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
        print_success "n8n is healthy"
    else
        print_error "n8n is not responding"
    fi
    
    # Check MinIO
    if curl -s http://localhost:9000/minio/health/live > /dev/null 2>&1; then
        print_success "MinIO is healthy"
    else
        print_error "MinIO is not responding"
    fi
    
    # Check Redis
    if docker exec robo-researcher-redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is healthy"
    else
        print_error "Redis is not responding"
    fi
    
    # Check PostgreSQL
    if docker exec robo-researcher-postgres pg_isready -U n8n > /dev/null 2>&1; then
        print_success "PostgreSQL is healthy"
    else
        print_error "PostgreSQL is not responding"
    fi
}

# Function to show URLs
show_urls() {
    echo ""
    print_success "🎉 ROBO-RESEARCHER-2000 Access URLs:"
    echo ""
    echo "📋 Service URLs:"
    echo "  • n8n (Workflow Engine):     http://localhost:5678"
    echo "  • MinIO (Storage):           http://localhost:9001"
    echo ""
    echo "🔐 Default Credentials:"
    echo "  • n8n:     admin / robo-researcher-2000"
    echo "  • MinIO:   minioadmin / minioadmin"
    echo ""
}

# Main script logic
case "${1:-help}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    update)
        update_services
        ;;
    clean)
        clean_services
        ;;
    health)
        check_health
        ;;
    urls)
        show_urls
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac
