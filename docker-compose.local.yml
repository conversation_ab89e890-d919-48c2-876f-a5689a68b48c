version: '3.8'

networks:
  robo-researcher-network:
    driver: bridge

volumes:
  robo-researcher-n8n-data:
  robo-researcher-minio-data:
  robo-researcher-postgres-data:

services:
  # PostgreSQL Database (shared by n8n and Wiki.js)
  robo-researcher-postgres:
    image: postgres:15-alpine
    container_name: robo-researcher-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n_password
    volumes:
      - robo-researcher-postgres-data:/var/lib/postgresql/data
      - ./infrastructure/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - robo-researcher-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n -d n8n"]
      interval: 30s
      timeout: 10s
      retries: 3

  # n8n - Workflow Automation
  robo-researcher-n8n:
    image: n8nio/n8n:latest
    container_name: robo-researcher-n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=${N8N_HOST:-localhost}
      - N8N_PORT=5678
      - N8N_PROTOCOL=${N8N_PROTOCOL:-http}
      - N8N_SECURE_COOKIE=false
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000
      - WEBHOOK_URL=${N8N_WEBHOOK_URL:-http://localhost:5678}
      - GENERIC_TIMEZONE=${GENERIC_TIMEZONE:-America/Mexico_City}
      - N8N_LOG_LEVEL=${N8N_LOG_LEVEL:-info}
      - N8N_METRICS=true
      - N8N_DIAGNOSTICS_ENABLED=true
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=robo-researcher-postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n_password
    volumes:
      - robo-researcher-n8n-data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows:ro
      - ./scripts:/home/<USER>/scripts:ro
      - ./data:/home/<USER>/data
    networks:
      - robo-researcher-network
    depends_on:
      - robo-researcher-postgres
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO - S3 Compatible Storage
  robo-researcher-minio:
    image: minio/minio:latest
    container_name: robo-researcher-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY:-minioadmin}
      - MINIO_BROWSER_REDIRECT_URL=http://localhost:9001
    volumes:
      - robo-researcher-minio-data:/data
    networks:
      - robo-researcher-network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching (optional but recommended)
  robo-researcher-redis:
    image: redis:7-alpine
    container_name: robo-researcher-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - robo-researcher-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Client Web Application Server (temporarily disabled due to Dockerfile issues)
  # robo-researcher-client:
  #   build:
  #     context: ./client
  #     dockerfile: Dockerfile
  #   container_name: robo-researcher-client
  #   restart: unless-stopped
  #   ports:
  #     - "80:80"
  #   environment:
  #     - N8N_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/robo-researcher
  #     - N8N_TEST_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/test-robo-researcher
  #     - API_BASE_URL=http://localhost:5678
  #   volumes:
  #     - ./client:/usr/share/nginx/html:ro
  #   networks:
  #     - robo-researcher-network
  #   depends_on:
  #     - robo-researcher-n8n
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:80/"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
