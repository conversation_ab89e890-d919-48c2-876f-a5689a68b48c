-- Initialize Wiki.js database
-- This script creates the necessary database and user for Wiki.js

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE wikijs'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'wikijs')\gexec

-- Create user if it doesn't exist
DO
$do$
BEGIN
   IF NOT EXISTS (
      SELECT FROM pg_catalog.pg_roles
      WHERE  rolname = 'wikijs') THEN

      CREATE ROLE wikijs LOGIN PASSWORD 'wikijs_password';
   END IF;
END
$do$;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE wikijs TO wikijs;

-- Connect to wikijs database and set up extensions
\c wikijs;

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- <PERSON> schema privileges
GRANT ALL ON SCHEMA public TO wikijs;
