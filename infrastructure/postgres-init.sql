-- PostgreSQL initialization script for ROBO-RESEARCHER-2000
-- Creates databases and users for n8n and Wiki.js

-- Create Wiki.js database and user
CREATE DATABASE wikijs;
CREATE USER wikijs WITH ENCRYPTED PASSWORD 'wikijs_password';
GRANT ALL PRIVILEGES ON DATABASE wikijs TO wikijs;

-- Grant necessary permissions
ALTER USER wikijs CREATEDB;

-- Create extensions if needed
\c n8n;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c wikijs;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Set default permissions
\c n8n;
GRANT ALL ON SCHEMA public TO n8n;

\c wikijs;
GRANT ALL ON SCHEMA public TO wikijs;
