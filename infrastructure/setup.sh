#!/bin/bash

# ROBO-RESEARCHER-2000 Infrastructure Setup Script
# This script sets up the complete infrastructure for the UX research automation system

set -e

echo "🤖 ROBO-RESEARCHER-2000 Infrastructure Setup"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Create necessary directories
create_directories() {
    print_status "Creating directory structure..."
    
    mkdir -p n8n/workflows
    mkdir -p n8n/backups
    mkdir -p minio/data
    mkdir -p wikijs/data
    mkdir -p scripts/modules
    mkdir -p logs
    
    print_success "Directory structure created"
}

# Copy environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f ../.env.example ]; then
            cp ../.env.example .env
            print_warning "Environment file created from template. Please edit .env with your configurations."
        else
            print_error ".env.example not found. Please create .env manually."
            exit 1
        fi
    else
        print_success "Environment file already exists"
    fi
}

# Start services
start_services() {
    print_status "Starting ROBO-RESEARCHER-2000 services..."
    
    # Pull latest images
    docker-compose pull
    
    # Start services
    docker-compose up -d
    
    print_success "Services started successfully"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for n8n
    print_status "Waiting for n8n to be ready..."
    timeout 120 bash -c 'until curl -f http://localhost:5678/healthz &>/dev/null; do sleep 2; done'
    
    # Wait for MinIO
    print_status "Waiting for MinIO to be ready..."
    timeout 120 bash -c 'until curl -f http://localhost:9000/minio/health/live &>/dev/null; do sleep 2; done'
    
    # Wait for Wiki.js
    print_status "Waiting for Wiki.js to be ready..."
    timeout 120 bash -c 'until curl -f http://localhost:3000 &>/dev/null; do sleep 2; done'
    
    print_success "All services are ready"
}

# Setup MinIO buckets
setup_minio() {
    print_status "Setting up MinIO buckets..."
    
    # Install MinIO client if not present
    if ! command -v mc &> /dev/null; then
        print_status "Installing MinIO client..."
        curl -O https://dl.min.io/client/mc/release/linux-amd64/mc
        chmod +x mc
        sudo mv mc /usr/local/bin/
    fi
    
    # Configure MinIO client
    mc alias set robo-researcher http://localhost:9000 minioadmin minioadmin
    
    # Create buckets
    mc mb robo-researcher/robo-researcher-data --ignore-existing
    mc mb robo-researcher/transcripts --ignore-existing
    mc mb robo-researcher/results --ignore-existing
    mc mb robo-researcher/presentations --ignore-existing
    mc mb robo-researcher/backups --ignore-existing
    
    # Set bucket policies (public read for results)
    mc anonymous set download robo-researcher/results
    
    print_success "MinIO buckets configured"
}

# Display service information
show_services_info() {
    echo ""
    echo "🎉 ROBO-RESEARCHER-2000 Infrastructure Ready!"
    echo "=============================================="
    echo ""
    echo "📊 Service URLs:"
    echo "  • n8n Workflow Engine:    http://localhost:5678"
    echo "    Username: admin"
    echo "    Password: robo-researcher-2000"
    echo ""
    echo "  • MinIO Storage Console:  http://localhost:9001"
    echo "    Username: minioadmin"
    echo "    Password: minioadmin"
    echo ""
    echo "  • Wiki.js Documentation: http://localhost:3000"
    echo "    (Complete setup in browser)"
    echo ""
    echo "🔧 Next Steps:"
    echo "  1. Configure Wiki.js in browser"
    echo "  2. Import n8n workflow from workflows/ directory"
    echo "  3. Configure API keys in .env file"
    echo "  4. Deploy client web application"
    echo ""
    echo "📝 Logs: docker-compose logs -f [service-name]"
    echo "🛑 Stop: docker-compose down"
    echo ""
}

# Main execution
main() {
    check_docker
    create_directories
    setup_environment
    start_services
    wait_for_services
    setup_minio
    show_services_info
}

# Run main function
main "$@"
