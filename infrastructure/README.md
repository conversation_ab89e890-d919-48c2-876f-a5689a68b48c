# ROBO-RESEARCHER-2000 Infrastructure

Esta carpeta contiene toda la configuración de infraestructura para el sistema ROBO-RESEARCHER-2000.

## 🏗️ Componentes

### Servicios Docker

- **n8n**: Motor de workflows y automatización
- **PostgreSQL**: Base de datos para n8n y Wiki.js
- **MinIO**: Almacenamiento S3-compatible
- **Wiki.js**: Plataforma de documentación
- **Redis**: Cache (opcional)

### Puertos

- **5678**: n8n Web Interface
- **9000**: MinIO API
- **9001**: <PERSON><PERSON> Console
- **3000**: Wiki.js
- **6379**: Redis

## 🚀 Instalación Rápida

```bash
# Ejecutar script de configuración automática
cd infrastructure
./setup.sh
```

## 📋 Instalación Manual

### 1. Preparar Entorno

```bash
# Copiar archivo de configuración
cp ../.env.example .env

# Editar configuraciones
nano .env
```

### 2. Iniciar Servicios

```bash
# Construir e iniciar contenedores
docker-compose up -d

# Verificar estado
docker-compose ps
```

### 3. Configurar MinIO

```bash
# Instalar cliente MinIO
curl -O https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc && sudo mv mc /usr/local/bin/

# Configurar alias
mc alias set robo-researcher http://localhost:9000 minioadmin minioadmin

# Crear buckets
mc mb robo-researcher/robo-researcher-data
mc mb robo-researcher/transcripts
mc mb robo-researcher/results
mc mb robo-researcher/presentations
```

### 4. Configurar Wiki.js

1. Abrir http://localhost:3000
2. Seguir wizard de configuración inicial
3. Configurar conexión a PostgreSQL:
   - Host: `robo-researcher-postgres`
   - Database: `wikijs`
   - User: `wikijs`
   - Password: `wikijs_password`

## 🔧 Comandos Útiles

### Docker Compose

```bash
# Ver logs
docker-compose logs -f [service-name]

# Reiniciar servicio
docker-compose restart [service-name]

# Detener todos los servicios
docker-compose down

# Detener y eliminar volúmenes
docker-compose down -v
```

### Backup y Restore

```bash
# Backup de n8n
docker-compose exec robo-researcher-n8n n8n export:workflow --all --output=/home/<USER>/.n8n/backups/

# Backup de base de datos
docker-compose exec robo-researcher-postgres pg_dump -U n8n n8n > backup_n8n.sql
```

### Monitoreo

```bash
# Estado de servicios
docker-compose ps

# Uso de recursos
docker stats

# Logs en tiempo real
docker-compose logs -f
```

## 🔒 Seguridad

### Credenciales por Defecto

**n8n:**
- Usuario: `admin`
- Contraseña: `robo-researcher-2000`

**MinIO:**
- Access Key: `minioadmin`
- Secret Key: `minioadmin`

**PostgreSQL:**
- Usuario n8n: `n8n` / `n8n_password`
- Usuario Wiki.js: `wikijs` / `wikijs_password`

### Recomendaciones

1. **Cambiar credenciales por defecto** en producción
2. **Configurar HTTPS** con reverse proxy (nginx/traefik)
3. **Habilitar autenticación** adicional si es necesario
4. **Configurar backups automáticos**
5. **Monitorear logs** regularmente

## 🌐 Acceso Externo

Para acceso desde internet, configurar reverse proxy:

```nginx
# Ejemplo nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /n8n/ {
        proxy_pass http://localhost:5678/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /minio/ {
        proxy_pass http://localhost:9001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /wiki/ {
        proxy_pass http://localhost:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🐛 Troubleshooting

### Problemas Comunes

1. **Puerto ocupado**: Cambiar puertos en docker-compose.yml
2. **Permisos**: Verificar permisos de carpetas de datos
3. **Memoria**: Verificar recursos disponibles con `docker stats`
4. **Red**: Verificar conectividad entre contenedores

### Logs Útiles

```bash
# n8n
docker-compose logs robo-researcher-n8n

# MinIO
docker-compose logs robo-researcher-minio

# Wiki.js
docker-compose logs robo-researcher-wikijs

# PostgreSQL
docker-compose logs robo-researcher-postgres
```

## 📊 Monitoreo

### Health Checks

Todos los servicios incluyen health checks automáticos:

```bash
# Verificar estado
docker-compose ps

# Health check manual
curl http://localhost:5678/healthz  # n8n
curl http://localhost:9000/minio/health/live  # MinIO
curl http://localhost:3000  # Wiki.js
```

### Métricas

n8n incluye métricas Prometheus en `/metrics` cuando está habilitado.
