# ROBO-RESEARCHER-2000 Infrastructure

This folder contains all infrastructure configuration for the ROBO-RESEARCHER-2000 system.

## 🏗️ Components

### Docker Services

- **n8n**: Workflow engine and automation
- **PostgreSQL**: Database for n8n and Wiki.js
- **MinIO**: S3-compatible storage
- **Wiki.js**: Documentation platform
- **Redis**: <PERSON><PERSON> (optional)

### Ports

- **5678**: n8n Web Interface
- **9000**: MinIO API
- **9001**: MinIO Console
- **3000**: Wiki.js
- **6379**: Redis

## 🚀 Quick Installation

```bash
# Run automatic setup script
cd infrastructure
./setup.sh
```

## 📋 Manual Installation

### 1. Prepare Environment

```bash
# Copy configuration file
cp ../.env.example .env

# Edit configurations
nano .env
```

### 2. Start Services

```bash
# Build and start containers
docker-compose up -d

# Check status
docker-compose ps
```

### 3. Configure MinIO

```bash
# Install MinIO client
curl -O https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc && sudo mv mc /usr/local/bin/

# Configure alias
mc alias set robo-researcher http://localhost:9000 minioadmin minioadmin

# Create buckets
mc mb robo-researcher/robo-researcher-data
mc mb robo-researcher/transcripts
mc mb robo-researcher/results
mc mb robo-researcher/presentations
```

### 4. Configure Wiki.js

1. Open http://localhost:3000
2. Follow initial configuration wizard
3. Configure PostgreSQL connection:
   - Host: `robo-researcher-postgres`
   - Database: `wikijs`
   - User: `wikijs`
   - Password: `wikijs_password`

## 🔧 Useful Commands

### Docker Compose

```bash
# Ver logs
docker-compose logs -f [service-name]

# Reiniciar servicio
docker-compose restart [service-name]

# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

### Backup y Restore

```bash
# Backup de n8n
docker-compose exec robo-researcher-n8n n8n export:workflow --all --output=/home/<USER>/.n8n/backups/

# Backup de base de datos
docker-compose exec robo-researcher-postgres pg_dump -U n8n n8n > backup_n8n.sql
```

### Monitoreo

```bash
# Estado de servicios
docker-compose ps

# Uso de recursos
docker stats

# Logs en tiempo real
docker-compose logs -f
```

## 🔒 Seguridad

### Default Credentials

**n8n:**
- User: `admin`
- Password: `robo-researcher-2000`

**MinIO:**
- Access Key: `minioadmin`
- Secret Key: `minioadmin`

**PostgreSQL:**
- Usuario n8n: `n8n` / `n8n_password`
- Usuario Wiki.js: `wikijs` / `wikijs_password`

### Recommendations

1. **Change default credentials** in production
2. **Configure HTTPS** with reverse proxy (nginx/traefik)
3. **Enable additional authentication** if necessary
4. **Configure automatic backups**
5. **Monitor logs** regularly

## 🌐 Acceso Externo

For internet access, configure reverse proxy:

```nginx
# Ejemplo nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /n8n/ {
        proxy_pass http://localhost:5678/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /minio/ {
        proxy_pass http://localhost:9001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /wiki/ {
        proxy_pass http://localhost:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🐛 Troubleshooting

### Problemas Comunes

1. **Puerto ocupado**: Cambiar puertos en docker-compose.yml
2. **Permisos**: Verificar permisos de carpetas de datos
3. **Memoria**: Verificar recursos disponibles con `docker stats`
4. **Red**: Verificar conectividad entre contenedores

### Useful Logs

```bash
# n8n
docker-compose logs robo-researcher-n8n

# MinIO
docker-compose logs robo-researcher-minio

# Wiki.js
docker-compose logs robo-researcher-wikijs

# PostgreSQL
docker-compose logs robo-researcher-postgres
```

## 📊 Monitoreo

### Health Checks

All services include automatic health checks:

```bash
# Verificar estado
docker-compose ps

# Health check manual
curl http://localhost:5678/healthz  # n8n
curl http://localhost:9000/minio/health/live  # MinIO
curl http://localhost:3000  # Wiki.js
```

### Metrics

n8n includes Prometheus metrics at `/metrics` when enabled.
