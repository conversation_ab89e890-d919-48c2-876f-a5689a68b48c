{"name": "ROBO-RESEARCHER-2000 Complete Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "robo-researcher", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "1. <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "robo-researcher"}, {"parameters": {"language": "python", "pythonCode": "# Step 2: Validate Input Data\nimport json\nimport re\nfrom datetime import datetime\n\ninput_data = $input.all()[0].json\nrequired_fields = ['project_name', 'email', 'transcription', 'api_keys']\nmissing_fields = [field for field in required_fields if field not in input_data or not input_data[field]]\n\nif missing_fields:\n    return [{'json': {'success': False, 'error': f'Missing required fields: {\", \".join(missing_fields)}', 'step': 'validation'}}]\n\nemail_pattern = r'^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'\nif not re.match(email_pattern, input_data['email']):\n    return [{'json': {'success': False, 'error': 'Invalid email format', 'step': 'validation'}}]\n\nif len(input_data['transcription']) < 100:\n    return [{'json': {'success': False, 'error': 'Transcription too short', 'step': 'validation'}}]\n\nexecution_id = f\"exec_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n\nreturn [{'json': {'success': True, 'execution_id': execution_id, **input_data, 'step': 'validation_complete', 'timestamp': datetime.now().isoformat()}}]"}, "id": "validate-input", "name": "2. Validate Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"method": "PUT", "url": "http://localhost:9000/robo-researcher-data/transcripts/{{ $json.execution_id }}/original.txt", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "text/plain"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "text", "body": "={{ $json.transcription }}"}, "id": "upload-to-minio", "name": "3. Upload to MinIO", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 300]}, {"parameters": {"language": "python", "pythonCode": "# Step 4: Text Preprocessing\nimport re\nfrom datetime import datetime\n\ndata = $input.all()[0].json\ntext = data['transcription']\nlanguage = data.get('language', 'en')\n\n# Remove timestamps and filler words\ntext = re.sub(r'\\[\\d{1,2}:\\d{2}:\\d{2}\\]', '', text)\nfillers = r'\\b(uh|uhm|um|hmm|like|you know|I mean)\\b' if language == 'en' else r'\\b(eh|ehm|mm|este|bueno|o sea)\\b'\ntext = re.sub(fillers, '', text, flags=re.IGNORECASE)\ntext = re.sub(r'\\s+', ' ', text).strip()\n\n# Extract segments\nsegments = []\nspeaker_pattern = r'^([A-Za-z0-9_]+):\\s*(.+?)(?=\\n[A-Za-z0-9_]+:|$)'\nmatches = list(re.finditer(speaker_pattern, text, re.MULTILINE | re.DOTALL))\n\nif matches:\n    for i, match in enumerate(matches):\n        speaker, content = match.group(1), match.group(2).strip()\n        if len(content) >= 10:\n            segments.append({'segment_id': i + 1, 'speaker': speaker, 'content': content, 'word_count': len(content.split())})\nelse:\n    if len(text) >= 10:\n        segments.append({'segment_id': 1, 'speaker': 'Unknown', 'content': text, 'word_count': len(text.split())})\n\nresult = {'processed_text': text, 'segments': segments, 'statistics': {'segment_count': len(segments)}}\n\nreturn [{'json': {**data, 'processed_transcription': result, 'step': 'preprocessing_complete', 'timestamp': datetime.now().isoformat()}}]"}, "id": "text-preprocessing", "name": "4. Text Preprocessing", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"language": "python", "pythonCode": "# Step 5: Segmentation\nfrom datetime import datetime\n\ndata = $input.all()[0].json\nsegments = data['processed_transcription']['segments']\n\ntopic_keywords = {\n    'navigation': ['navigate', 'menu', 'find', 'search'],\n    'usability': ['easy', 'difficult', 'hard', 'simple'],\n    'performance': ['slow', 'fast', 'loading', 'wait'],\n    'design': ['design', 'layout', 'color', 'visual'],\n    'emotion': ['frustrated', 'happy', 'satisfied', 'annoyed']\n}\n\nenhanced_segments = []\nfor segment in segments:\n    content_lower = segment['content'].lower()\n    detected_topics = [topic for topic, keywords in topic_keywords.items() if any(keyword in content_lower for keyword in keywords)]\n    if not detected_topics:\n        detected_topics = ['general']\n    \n    enhanced_segments.append({**segment, 'topics': detected_topics, 'primary_topic': detected_topics[0]})\n\ntopic_distribution = {}\nfor segment in enhanced_segments:\n    for topic in segment['topics']:\n        topic_distribution[topic] = topic_distribution.get(topic, 0) + 1\n\nreturn [{'json': {**data, 'segments': enhanced_segments, 'segmentation_stats': {'total_segments': len(enhanced_segments), 'topic_distribution': topic_distribution}, 'step': 'segmentation_complete', 'timestamp': datetime.now().isoformat()}}]"}, "id": "segmentation", "name": "5. Segmentation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"language": "python", "pythonCode": "# Step 6: Deductive Coding\nfrom datetime import datetime\n\ndata = $input.all()[0].json\nsegments = data['segments']\n\ndeductive_codes = {\n    'frustration': ['frustrated', 'annoying', 'difficult', 'confusing'],\n    'satisfaction': ['satisfied', 'happy', 'easy', 'clear'],\n    'navigation': ['menu', 'navigate', 'search', 'find'],\n    'performance': ['slow', 'fast', 'loading', 'wait']\n}\n\ncoded_segments = []\nfor segment in segments:\n    content_lower = segment['content'].lower()\n    matched_codes = [code for code, keywords in deductive_codes.items() if any(keyword in content_lower for keyword in keywords)]\n    \n    # Calculate intensity\n    intensity = 1.0\n    if any(word in content_lower for word in ['very', 'extremely', 'really']):\n        intensity += 1.5\n    if '!' in segment['content']:\n        intensity += 0.5\n    intensity = min(5.0, max(1.0, intensity))\n    \n    # Detect emotion\n    positive_words = ['good', 'great', 'easy', 'clear', 'like']\n    negative_words = ['bad', 'difficult', 'hate', 'frustrated']\n    pos_count = sum(1 for word in positive_words if word in content_lower)\n    neg_count = sum(1 for word in negative_words if word in content_lower)\n    \n    emotion = 'positive' if pos_count > neg_count else 'negative' if neg_count > pos_count else 'neutral'\n    \n    coded_segments.append({**segment, 'codes': matched_codes, 'intensity': round(intensity, 1), 'emotion': emotion})\n\ncode_frequencies = {}\nfor segment in coded_segments:\n    for code in segment['codes']:\n        code_frequencies[code] = code_frequencies.get(code, 0) + 1\n\nreturn [{'json': {**data, 'coded_segments': coded_segments, 'deductive_coding_stats': {'code_frequencies': code_frequencies}, 'step': 'deductive_coding_complete', 'timestamp': datetime.now().isoformat()}}]"}, "id": "deductive-coding", "name": "6. Deductive Coding", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $json.api_keys.openrouter }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "{\n  \"model\": \"anthropic/claude-3-sonnet\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a UX researcher. Analyze segments and suggest 5 open codes not covered by existing codes. Return JSON: [{\\\"code\\\": \\\"name\\\", \\\"description\\\": \\\"desc\\\"}]\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Existing codes: {{ $json.deductive_coding_stats.code_frequencies }}\\n\\nSegments: {{ $json.coded_segments | slice(0, 5) | map(item => item.content) | join('\\n---\\n') }}\"\n    }\n  ],\n  \"max_tokens\": 500,\n  \"temperature\": 0.3\n}"}, "id": "open-coding-ai", "name": "7. Open Coding AI", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 300]}, {"parameters": {"language": "python", "pythonCode": "# Step 8: Category Grouping\nfrom datetime import datetime\nimport json\n\ndata = $input.all()[0].json\ncoded_segments = data['coded_segments']\n\n# Get AI suggestions if available\nai_response = data.get('choices', [{}])[0].get('message', {}).get('content', '[]')\ntry:\n    ai_suggestions = json.loads(ai_response)\nexcept:\n    ai_suggestions = []\n\n# Group codes into categories\ncategories = {\n    'emotional_responses': ['frustration', 'satisfaction', 'confusion', 'delight'],\n    'interaction_patterns': ['navigation', 'input_methods', 'error_recovery'],\n    'usability_factors': ['efficiency', 'learnability', 'memorability'],\n    'contextual_factors': ['device_context', 'environment', 'time_pressure']\n}\n\n# Add AI-suggested codes to appropriate categories\nfor suggestion in ai_suggestions:\n    code_name = suggestion.get('code', '')\n    if code_name and code_name not in [code for cat_codes in categories.values() for code in cat_codes]:\n        # Simple categorization based on description\n        description = suggestion.get('description', '').lower()\n        if any(word in description for word in ['emotion', 'feel', 'mood']):\n            categories['emotional_responses'].append(code_name)\n        elif any(word in description for word in ['interact', 'click', 'tap']):\n            categories['interaction_patterns'].append(code_name)\n        else:\n            categories['usability_factors'].append(code_name)\n\n# Calculate category frequencies\ncategory_frequencies = {}\nfor segment in coded_segments:\n    for code in segment['codes']:\n        for category, codes in categories.items():\n            if code in codes:\n                category_frequencies[category] = category_frequencies.get(category, 0) + 1\n                break\n\nreturn [{'json': {**data, 'categories': categories, 'category_frequencies': category_frequencies, 'ai_suggestions': ai_suggestions, 'step': 'category_grouping_complete', 'timestamp': datetime.now().isoformat()}}]"}, "id": "category-grouping", "name": "8. Category Grouping", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"language": "python", "pythonCode": "# Step 9: Affinity Mapping\nfrom datetime import datetime\nimport json\n\ndata = $input.all()[0].json\ncoded_segments = data['coded_segments']\ncategories = data['categories']\n\n# Generate affinity map data for visualization\naffinity_groups = {}\nfor category, codes in categories.items():\n    group_segments = []\n    for segment in coded_segments:\n        if any(code in codes for code in segment['codes']):\n            group_segments.append({\n                'id': segment['segment_id'],\n                'content': segment['content'][:100] + '...' if len(segment['content']) > 100 else segment['content'],\n                'codes': segment['codes'],\n                'intensity': segment['intensity'],\n                'emotion': segment['emotion']\n            })\n    \n    if group_segments:\n        affinity_groups[category] = {\n            'segments': group_segments,\n            'count': len(group_segments),\n            'avg_intensity': round(sum(s['intensity'] for s in group_segments) / len(group_segments), 2)\n        }\n\n# Generate Excalidraw-compatible JSON structure\nexcalidraw_data = {\n    'type': 'excalidraw',\n    'version': 2,\n    'source': 'robo-researcher-2000',\n    'elements': [],\n    'appState': {'gridSize': null, 'viewBackgroundColor': '#ffffff'}\n}\n\n# Add affinity groups as rectangles with text\ny_offset = 100\nfor i, (category, group_data) in enumerate(affinity_groups.items()):\n    x_offset = 100 + (i * 300)\n    \n    # Category header\n    excalidraw_data['elements'].append({\n        'type': 'rectangle',\n        'x': x_offset,\n        'y': y_offset,\n        'width': 250,\n        'height': 50,\n        'backgroundColor': '#e1f5fe',\n        'strokeColor': '#01579b',\n        'fillStyle': 'solid'\n    })\n    \n    excalidraw_data['elements'].append({\n        'type': 'text',\n        'x': x_offset + 10,\n        'y': y_offset + 15,\n        'text': f\"{category.replace('_', ' ').title()}\\n({group_data['count']} segments)\",\n        'fontSize': 16,\n        'fontFamily': 1\n    })\n    \n    # Add segment cards\n    for j, segment in enumerate(group_data['segments'][:5]):  # Limit to 5 per group\n        card_y = y_offset + 80 + (j * 60)\n        \n        excalidraw_data['elements'].append({\n            'type': 'rectangle',\n            'x': x_offset,\n            'y': card_y,\n            'width': 250,\n            'height': 50,\n            'backgroundColor': '#fff3e0' if segment['emotion'] == 'positive' else '#ffebee' if segment['emotion'] == 'negative' else '#f5f5f5',\n            'strokeColor': '#666666',\n            'fillStyle': 'solid'\n        })\n        \n        excalidraw_data['elements'].append({\n            'type': 'text',\n            'x': x_offset + 5,\n            'y': card_y + 5,\n            'text': f\"P{segment['id']}: {segment['content'][:40]}...\",\n            'fontSize': 12,\n            'fontFamily': 1\n        })\n\nreturn [{'json': {**data, 'affinity_groups': affinity_groups, 'excalidraw_data': excalidraw_data, 'step': 'affinity_mapping_complete', 'timestamp': datetime.now().isoformat()}}]"}, "id": "affinity-mapping", "name": "9. Affinity Mapping", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}, {"parameters": {"language": "python", "pythonCode": "# Step 10: Quantitative Analysis\nfrom datetime import datetime\nimport statistics\n\ndata = $input.all()[0].json\ncoded_segments = data['coded_segments']\n\n# Basic statistics\ntotal_segments = len(coded_segments)\ntotal_codes = sum(len(segment['codes']) for segment in coded_segments)\nunique_codes = len(set(code for segment in coded_segments for code in segment['codes']))\nparticipants = len(set(segment['speaker'] for segment in coded_segments))\n\n# Intensity analysis\nintensities = [segment['intensity'] for segment in coded_segments]\navg_intensity = round(statistics.mean(intensities), 2)\nmedian_intensity = round(statistics.median(intensities), 2)\nintensity_std = round(statistics.stdev(intensities) if len(intensities) > 1 else 0, 2)\n\n# Emotion distribution\nemotion_counts = {}\nfor segment in coded_segments:\n    emotion = segment['emotion']\n    emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1\n\nemotion_percentages = {emotion: round((count / total_segments) * 100, 1) for emotion, count in emotion_counts.items()}\n\n# Code frequency analysis\ncode_frequencies = {}\nfor segment in coded_segments:\n    for code in segment['codes']:\n        code_frequencies[code] = code_frequencies.get(code, 0) + 1\n\ntop_codes = dict(sorted(code_frequencies.items(), key=lambda x: x[1], reverse=True)[:10])\n\n# Participant analysis\nparticipant_stats = {}\nfor segment in coded_segments:\n    speaker = segment['speaker']\n    if speaker not in participant_stats:\n        participant_stats[speaker] = {'segments': 0, 'codes': 0, 'avg_intensity': 0, 'intensities': []}\n    \n    participant_stats[speaker]['segments'] += 1\n    participant_stats[speaker]['codes'] += len(segment['codes'])\n    participant_stats[speaker]['intensities'].append(segment['intensity'])\n\nfor speaker, stats in participant_stats.items():\n    stats['avg_intensity'] = round(statistics.mean(stats['intensities']), 2)\n    del stats['intensities']  # Remove raw data\n\n# Co-occurrence analysis\nco_occurrences = {}\nfor segment in coded_segments:\n    codes = segment['codes']\n    if len(codes) > 1:\n        for i, code1 in enumerate(codes):\n            for code2 in codes[i+1:]:\n                pair = tuple(sorted([code1, code2]))\n                co_occurrences[pair] = co_occurrences.get(pair, 0) + 1\n\ntop_co_occurrences = dict(sorted(co_occurrences.items(), key=lambda x: x[1], reverse=True)[:5])\n\nquantitative_results = {\n    'basic_statistics': {\n        'total_segments': total_segments,\n        'total_codes': total_codes,\n        'unique_codes': unique_codes,\n        'participants': participants,\n        'avg_codes_per_segment': round(total_codes / total_segments, 2)\n    },\n    'intensity_analysis': {\n        'average': avg_intensity,\n        'median': median_intensity,\n        'std_deviation': intensity_std,\n        'min': min(intensities),\n        'max': max(intensities)\n    },\n    'emotion_distribution': emotion_percentages,\n    'code_frequencies': top_codes,\n    'participant_analysis': participant_stats,\n    'co_occurrences': {f\"{pair[0]} + {pair[1]}\": count for pair, count in top_co_occurrences.items()}\n}\n\nreturn [{'json': {**data, 'quantitative_analysis': quantitative_results, 'step': 'quantitative_analysis_complete', 'timestamp': datetime.now().isoformat()}}]"}, "id": "quantitative-analysis", "name": "10. Quantitative Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2220, 300]}], "connections": {"1. Webhook Trigger": {"main": [[{"node": "2. Validate Input", "type": "main", "index": 0}]]}, "2. Validate Input": {"main": [[{"node": "3. Upload to MinIO", "type": "main", "index": 0}]]}, "3. Upload to MinIO": {"main": [[{"node": "4. Text Preprocessing", "type": "main", "index": 0}]]}, "4. Text Preprocessing": {"main": [[{"node": "5. Segmentation", "type": "main", "index": 0}]]}, "5. Segmentation": {"main": [[{"node": "6. Deductive Coding", "type": "main", "index": 0}]]}, "6. Deductive Coding": {"main": [[{"node": "7. Open Coding AI", "type": "main", "index": 0}]]}, "7. Open Coding AI": {"main": [[{"node": "8. Category Grouping", "type": "main", "index": 0}]]}, "8. Category Grouping": {"main": [[{"node": "9. Affinity Mapping", "type": "main", "index": 0}]]}, "9. Affinity Mapping": {"main": [[{"node": "10. Quantitative Analysis", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner"}, "versionId": "1.0", "meta": {"templateCredsSetupCompleted": true}, "id": "main-workflow-complete", "tags": [{"createdAt": "2025-01-17T00:00:00.000Z", "updatedAt": "2025-01-17T00:00:00.000Z", "id": "robo-researcher", "name": "robo-researcher"}]}