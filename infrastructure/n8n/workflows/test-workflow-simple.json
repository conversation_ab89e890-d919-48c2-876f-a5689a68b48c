{"name": "ROBO-RESEARCHER-2000 Test Workflow (Simple)", "nodes": [{"parameters": {"httpMethod": "POST", "path": "test-robo-researcher", "responseMode": "responseNode", "options": {}}, "id": "webhook-test", "name": "Test Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "test-robo-researcher"}, {"parameters": {"language": "python", "pythonCode": "# Simple Test Processor for ROBO-RESEARCHER-2000\nimport json\nfrom datetime import datetime\n\n# Get input data\ninput_data = $input.all()[0].json\n\n# Validate basic structure\nrequired_fields = ['project_name', 'email', 'transcription']\nmissing_fields = []\n\nfor field in required_fields:\n    if field not in input_data or not input_data[field]:\n        missing_fields.append(field)\n\nif missing_fields:\n    return [{\n        'json': {\n            'success': False,\n            'error': f'Missing required fields: {\", \".join(missing_fields)}',\n            'timestamp': datetime.now().isoformat()\n        }\n    }]\n\n# Simulate basic processing\ntranscription = input_data['transcription']\nword_count = len(transcription.split())\nchar_count = len(transcription)\n\n# Simple sentiment analysis\npositive_words = ['good', 'great', 'excellent', 'easy', 'clear', 'helpful', 'useful']\nnegative_words = ['bad', 'terrible', 'difficult', 'confusing', 'frustrating', 'slow']\n\ntranscription_lower = transcription.lower()\npositive_count = sum(1 for word in positive_words if word in transcription_lower)\nnegative_count = sum(1 for word in negative_words if word in transcription_lower)\n\nif positive_count > negative_count:\n    overall_sentiment = 'positive'\nelif negative_count > positive_count:\n    overall_sentiment = 'negative'\nelse:\n    overall_sentiment = 'neutral'\n\n# Simulate processing steps\nprocessing_steps = [\n    {'step': 1, 'name': 'Input Validation', 'status': 'completed', 'duration': '0.1s'},\n    {'step': 2, 'name': 'Text Analysis', 'status': 'completed', 'duration': '0.3s'},\n    {'step': 3, 'name': 'Sentiment Detection', 'status': 'completed', 'duration': '0.2s'},\n    {'step': 4, 'name': 'Basic Statistics', 'status': 'completed', 'duration': '0.1s'}\n]\n\n# Generate test results\ntest_results = {\n    'success': True,\n    'project_name': input_data['project_name'],\n    'email': input_data['email'],\n    'analysis_summary': {\n        'word_count': word_count,\n        'character_count': char_count,\n        'overall_sentiment': overall_sentiment,\n        'positive_indicators': positive_count,\n        'negative_indicators': negative_count\n    },\n    'processing_steps': processing_steps,\n    'simulated_insights': [\n        {\n            'title': 'Sample Insight 1',\n            'description': 'Users show mixed reactions to the interface',\n            'confidence': 'medium',\n            'priority': 'high'\n        },\n        {\n            'title': 'Sample Insight 2', \n            'description': 'Navigation patterns suggest usability issues',\n            'confidence': 'high',\n            'priority': 'medium'\n        }\n    ],\n    'next_steps': [\n        'Import full workflow for complete analysis',\n        'Configure API credentials for AI analysis',\n        'Set up MinIO and Wiki.js integration',\n        'Test with larger transcription samples'\n    ],\n    'estimated_full_processing_time': '15-20 minutes',\n    'test_mode': True,\n    'timestamp': datetime.now().isoformat()\n}\n\nreturn [{'json': test_results}]"}, "id": "test-processor", "name": "Test Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "test-response", "name": "Test Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Test Webhook": {"main": [[{"node": "Test Processor", "type": "main", "index": 0}]]}, "Test Processor": {"main": [[{"node": "Test Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner"}, "versionId": "test-1.0", "meta": {"templateCredsSetupCompleted": true}, "id": "test-workflow-simple", "tags": [{"createdAt": "2025-01-17T00:00:00.000Z", "updatedAt": "2025-01-17T00:00:00.000Z", "id": "test", "name": "test"}]}