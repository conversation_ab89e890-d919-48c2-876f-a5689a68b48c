# ROBO-RESEARCHER-2000 Workflow Documentation

## Overview

The main workflow consists of 17 automated steps that transform raw transcription data into actionable UX research insights. Each step is implemented as an n8n node with specific functionality.

## Workflow Steps

### Phase 1: Data Ingestion and Validation (Steps 1-3)

#### 1. Webhook Trigger
- **Node Type**: `n8n-nodes-base.webhook`
- **Purpose**: Receives data from the client web application
- **Input**: JSON payload with project metadata, transcription, and API keys
- **Output**: Raw input data for processing

#### 2. Validate Input
- **Node Type**: `n8n-nodes-base.code` (Python)
- **Purpose**: Validates required fields, email format, and data quality
- **Validation Rules**:
  - Required fields: project_name, email, transcription, api_keys
  - Email format validation using regex
  - Minimum transcription length (100 characters)
  - API key presence validation
- **Output**: Validated data with execution ID

#### 3. Upload to MinIO
- **Node Type**: `n8n-nodes-base.httpRequest`
- **Purpose**: Stores original transcription in MinIO S3-compatible storage
- **Method**: PUT request to MinIO endpoint
- **Path**: `/robo-researcher-data/transcripts/{execution_id}/original.txt`

### Phase 2: Text Processing (Steps 4-5)

#### 4. Text Preprocessing
- **Node Type**: `n8n-nodes-base.code` (Python)
- **Purpose**: Cleans and normalizes transcription text
- **Operations**:
  - Remove timestamps `[HH:MM:SS]`
  - Remove filler words (language-specific)
  - Normalize whitespace
  - Extract speaker segments
- **Output**: Processed text with speaker segments

#### 5. Segmentation
- **Node Type**: `n8n-nodes-base.code` (Python)
- **Purpose**: Segments text by topics and themes
- **Topic Categories**:
  - Navigation (menu, search, find)
  - Usability (easy, difficult, simple)
  - Performance (slow, fast, loading)
  - Design (layout, color, visual)
  - Emotion (frustrated, happy, satisfied)
- **Output**: Enhanced segments with topic assignments

### Phase 3: Qualitative Coding (Steps 6-8)

#### 6. Deductive Coding
- **Node Type**: `n8n-nodes-base.code` (Python)
- **Purpose**: Applies predefined codes to text segments
- **Code Categories**:
  - Emotional responses (frustration, satisfaction)
  - Interaction patterns (navigation, input methods)
  - Usability factors (efficiency, clarity)
- **Metrics**: Intensity scoring (1-5), emotion detection
- **Output**: Coded segments with intensity and emotion data

#### 7. Open Coding AI
- **Node Type**: `n8n-nodes-base.httpRequest`
- **Purpose**: Uses AI to suggest emergent codes not covered by deductive coding
- **API**: OpenRouter (Claude-3-Sonnet)
- **Prompt**: Analyzes segments and suggests 5-10 additional codes
- **Output**: AI-suggested codes with descriptions

#### 8. Category Grouping
- **Node Type**: `n8n-nodes-base.code` (Python)
- **Purpose**: Groups codes into higher-level categories
- **Categories**:
  - Emotional responses
  - Interaction patterns
  - Usability factors
  - Contextual factors
- **Output**: Categorized codes with frequency analysis

### Phase 4: Analysis and Visualization (Steps 9-10)

#### 9. Affinity Mapping
- **Node Type**: `n8n-nodes-base.code` (Python)
- **Purpose**: Creates affinity groups and generates visualization data
- **Visualization**: Excalidraw-compatible JSON structure
- **Features**:
  - Groups segments by category
  - Color-codes by emotion (positive/negative/neutral)
  - Calculates group statistics
- **Output**: Affinity groups and Excalidraw visualization data

#### 10. Quantitative Analysis
- **Node Type**: `n8n-nodes-base.code` (Python)
- **Purpose**: Performs statistical analysis on coded data
- **Metrics**:
  - Basic statistics (segments, codes, participants)
  - Intensity analysis (mean, median, std deviation)
  - Emotion distribution percentages
  - Code frequency rankings
  - Participant analysis
  - Co-occurrence patterns
- **Output**: Comprehensive quantitative results

### Phase 5: Insight Generation (Steps 11-15)

#### 11. Pattern Detection
- **Node Type**: `n8n-nodes-base.httpRequest` (AI)
- **Purpose**: Uses AI to detect behavioral patterns and outliers
- **Analysis Types**:
  - Recurring behavior sequences
  - Significant outliers
  - Tensions and contradictions
  - Co-occurrence patterns
- **Output**: Identified patterns with evidence

#### 12. Insight Generation
- **Node Type**: `n8n-nodes-base.httpRequest` (AI)
- **Purpose**: Generates structured UX insights from patterns
- **Insight Structure**: [User Type] + [Action] + [Context] + [Root Cause] + [Business Impact]
- **Scoring**: ICE methodology (Impact, Confidence, Ease)
- **Output**: Prioritized insights with supporting evidence

#### 13. Archetype Creation
- **Node Type**: `n8n-nodes-base.code` (Python)
- **Purpose**: Creates user archetypes based on behavioral patterns
- **Components**:
  - Representative quotes
  - Jobs to be Done
  - Pain points
  - Behavioral characteristics
- **Output**: User archetypes with detailed profiles

#### 14. HMW Generation
- **Node Type**: `n8n-nodes-base.httpRequest` (AI)
- **Purpose**: Generates "How Might We" questions from insights
- **Format**: Opportunity-focused questions with constraints
- **Categories**: Design, functionality, experience, business
- **Output**: Prioritized HMW questions

#### 15. Opportunity Prioritization
- **Node Type**: `n8n-nodes-base.code` (Python)
- **Purpose**: Prioritizes opportunities using RICE methodology
- **RICE Factors**:
  - Reach: Number of users affected
  - Impact: Degree of impact per user
  - Confidence: Certainty in estimates
  - Effort: Resources required
- **Output**: Ranked opportunities with RICE scores

### Phase 6: Output Generation (Steps 16-17)

#### 16. Presentation Generation
- **Node Type**: `n8n-nodes-base.code` (Python)
- **Purpose**: Generates presentation using Marp
- **Content Sections**:
  - Executive summary
  - Key findings
  - User archetypes
  - Insights and recommendations
  - Prioritized opportunities
  - Next steps
- **Formats**: PPTX, PDF, HTML
- **Output**: Generated presentation files

#### 17. Documentation & Email
- **Node Type**: `n8n-nodes-base.httpRequest` + `n8n-nodes-base.code`
- **Purpose**: Creates Wiki.js documentation and sends email notification
- **Documentation**: Structured markdown with all analysis results
- **Email**: Summary with download links and key insights
- **Storage**: Final results stored in MinIO for access

## Configuration Requirements

### Environment Variables
```bash
N8N_WEBHOOK_URL=http://localhost:5678/webhook/robo-researcher
MINIO_ENDPOINT=http://localhost:9000
WIKIJS_URL=http://localhost:3000
OPENROUTER_API_KEY=your_api_key_here
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
```

### Credentials in n8n
1. **OpenRouter API**: HTTP Request credential with Bearer token
2. **MinIO S3**: S3 credential with access/secret keys
3. **SMTP**: Email credential for notifications
4. **Wiki.js API**: HTTP Request credential with API token

## Error Handling

### Validation Errors
- Missing required fields
- Invalid email format
- Insufficient transcription length
- Missing API keys

### Processing Errors
- File upload failures
- AI API timeouts
- Insufficient data for analysis
- Presentation generation failures

### Recovery Mechanisms
- Automatic retries for API calls
- Fallback processing for AI failures
- Error notifications via email
- Partial results preservation

## Performance Considerations

### Expected Processing Times
- Steps 1-5 (Preprocessing): 1-2 minutes
- Steps 6-10 (Coding & Analysis): 3-5 minutes
- Steps 11-15 (AI Insights): 5-8 minutes
- Steps 16-17 (Output Generation): 2-3 minutes
- **Total**: 15-20 minutes for typical transcription

### Resource Requirements
- **CPU**: 2-4 cores recommended
- **Memory**: 4-6 GB RAM
- **Storage**: 10-20 GB for data and results
- **Network**: Stable internet for AI API calls

## Monitoring and Debugging

### Workflow Execution
- Each step includes timestamp and status
- Error messages with specific failure points
- Progress tracking through execution ID

### Logging
- Detailed logs for each processing step
- API call logs with response times
- Error logs with stack traces

### Health Checks
- Service availability monitoring
- API endpoint health checks
- Storage accessibility verification

## Customization Options

### Coding Schemes
- Modify deductive codes in configuration
- Adjust AI prompts for domain-specific analysis
- Custom category groupings

### Analysis Parameters
- Intensity calculation weights
- Emotion detection thresholds
- Statistical significance levels

### Output Formats
- Custom presentation templates
- Alternative visualization formats
- Configurable email templates

This workflow provides a comprehensive, automated solution for UX research analysis while maintaining flexibility for customization and adaptation to specific research needs.
