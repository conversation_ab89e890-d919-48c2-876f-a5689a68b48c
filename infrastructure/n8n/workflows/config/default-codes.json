{"deductive_codes": {"user_experience": {"frustration": {"keywords": ["frustrated", "annoying", "irritating", "difficult", "complicated", "confusing", "frustrado", "molesto", "irritante", "dif<PERSON><PERSON><PERSON>", "complicado", "confuso"], "description": "User frustration moments", "category": "negative_emotion"}, "satisfaction": {"keywords": ["satisfied", "happy", "easy", "intuitive", "clear", "simple", "satisfecho", "contento", "f<PERSON><PERSON><PERSON>", "intuitivo", "claro", "simple"], "description": "User satisfaction moments", "category": "positive_emotion"}, "confusion": {"keywords": ["don't understand", "confused", "lost", "don't know", "unclear", "no entiendo", "confuso", "perdido", "no sé"], "description": "Confusion or lack of clarity moments", "category": "cognitive_load"}, "efficiency": {"keywords": ["fast", "slow", "time", "efficient", "delay", "wait", "<PERSON><PERSON><PERSON><PERSON>", "lento", "tiempo", "eficiente", "demora", "espera"], "description": "Perceptions about speed and efficiency", "category": "performance"}}, "interaction_patterns": {"navigation": {"keywords": ["menu", "navigate", "search", "find", "go to", "back", "menú", "<PERSON>gar", "buscar", "encontrar", "ir a", "volver"], "description": "Navigation and search patterns", "category": "behavior"}, "input_methods": {"keywords": ["type", "click", "tap", "select", "drag", "escribir", "tocar", "seleccionar", "arra<PERSON><PERSON>"], "description": "Input and interaction methods", "category": "behavior"}, "error_recovery": {"keywords": ["error", "mistake", "correct", "undo", "go back", "equivocación", "corregir", "<PERSON><PERSON><PERSON>", "volver atrás"], "description": "Error recovery patterns", "category": "error_handling"}}, "context_factors": {"device_context": {"keywords": ["mobile", "desktop", "tablet", "screen", "keyboard", "móvil", "pantalla", "teclado"], "description": "Device context used", "category": "context"}, "environment": {"keywords": ["home", "office", "public", "noise", "privacy", "casa", "oficina", "público", "ruido", "privacidad"], "description": "Environmental context of use", "category": "context"}, "time_pressure": {"keywords": ["hurry", "urgent", "time", "deadline", "fast", "prisa", "urgente", "tiempo", "<PERSON><PERSON><PERSON><PERSON>"], "description": "Time pressure during use", "category": "context"}}, "goals_and_tasks": {"primary_goals": {"keywords": ["goal", "objective", "want", "need", "looking for", "objetivo", "meta", "quiero", "<PERSON>cesito", "busco"], "description": "User primary objectives", "category": "goals"}, "secondary_tasks": {"keywords": ["also", "additionally", "while", "meanwhile", "también", "<PERSON><PERSON><PERSON>", "mientras", "aprovecho"], "description": "Secondary tasks or multitasking", "category": "goals"}, "completion": {"keywords": ["finished", "completed", "done", "ready", "success", "terminé", "completé", "listo", "finalizado", "éxito"], "description": "Task completion indicators", "category": "success"}}}, "coding_rules": {"multiple_codes": true, "minimum_segment_length": 10, "context_window": 50, "emotion_detection": true, "intensity_scoring": true}, "analysis_parameters": {"frequency_threshold": 3, "co_occurrence_threshold": 0.3, "sentiment_analysis": true, "entity_extraction": true, "language": "es"}, "output_format": {"include_quotes": true, "include_timestamps": true, "include_participant_id": true, "include_context": true, "max_quote_length": 200}}