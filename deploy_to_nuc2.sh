#!/bin/bash

# ROBO-RESEARCHER-2000 Deployment Script for nuc2 Container 200
# This script deploys the complete system to the existing container on nuc2

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_ID="200"
CONTAINER_HOST="robo-researcher"
NUC2_IP="*************"
PROXY_HOST="prox.stargety.in"
REPO_URL="https://github.com/c42705/robo-researcher-2000.git"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ROBO-RESEARCHER-2000                      ║"
    echo "║                 nuc2 Container Deployment                    ║"
    echo "║                                                              ║"
    echo "║  Target: Container 200 (robo-researcher) on nuc2            ║"
    echo "║  IP: *************                                          ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Main deployment function
deploy_to_container() {
    print_banner

    log_info "Starting deployment to nuc2 container 200..."

    # Step 1: Connect to nuc2 and then to container, update system
    log_info "Step 1: Updating system packages..."
    ssh root@${PROXY_HOST} "ssh nuc2 'pct exec ${CONTAINER_ID} -- apt update && pct exec ${CONTAINER_ID} -- apt upgrade -y'"

    # Step 2: Install required packages
    log_info "Step 2: Installing Docker and dependencies..."
    ssh root@${PROXY_HOST} "ssh nuc2 'pct exec ${CONTAINER_ID} -- apt install -y docker.io docker-compose git curl wget nano htop'"

    # Step 3: Enable Docker service
    log_info "Step 3: Enabling Docker service..."
    ssh root@${PROXY_HOST} "ssh nuc2 'pct exec ${CONTAINER_ID} -- systemctl enable docker'"
    ssh root@${PROXY_HOST} "ssh nuc2 'pct exec ${CONTAINER_ID} -- systemctl start docker'"
    
    # Step 4: Clone repository
    log_info "Step 4: Cloning ROBO-RESEARCHER-2000 repository..."
    ssh root@${PROXY_HOST} "ssh nuc2 'pct exec ${CONTAINER_ID} -- git clone ${REPO_URL} /opt/robo-researcher-2000'"

    # Step 5: Create environment configuration
    log_info "Step 5: Creating environment configuration..."
    ssh root@${PROXY_HOST} "ssh nuc2 'pct exec ${CONTAINER_ID} -- bash -c \"cat > /opt/robo-researcher-2000/.env << EOF
# Core Database Configuration
POSTGRES_DB=robo_researcher
POSTGRES_USER=robo_user
POSTGRES_PASSWORD=robo_pass_2000

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000
N8N_HOST=robo.stargety.com
N8N_PROTOCOL=https
N8N_SECURE_COOKIE=false

# MinIO Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin

# Wiki.js Configuration
WIKIJS_DB_PASSWORD=wikijs_pass_2000

# Email Configuration (Stargety SMTP)
SMTP_HOST=mail.stargety.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# API Configuration
OPENROUTER_API_KEY=your_openrouter_key

# Client Configuration
N8N_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/robo-researcher
N8N_TEST_WEBHOOK_URL=http://robo-researcher-n8n:5678/webhook/test-robo-researcher
API_BASE_URL=http://localhost:5678
NODE_ENV=production
EOF\""'

    # Step 6: Deploy Docker containers
    log_info "Step 6: Deploying Docker containers..."
    ssh root@${PROXY_HOST} "ssh nuc2 'pct exec ${CONTAINER_ID} -- bash -c \"cd /opt/robo-researcher-2000 && docker-compose -f infrastructure/docker-compose.yml up -d --build\"'"

    # Step 7: Wait for services to start
    log_info "Step 7: Waiting for services to start..."
    sleep 60

    # Step 8: Verify deployment
    log_info "Step 8: Verifying deployment..."
    ssh root@${PROXY_HOST} "ssh nuc2 'pct exec ${CONTAINER_ID} -- docker ps'"

    # Step 9: Check service health
    log_info "Step 9: Checking service health..."
    ssh root@${PROXY_HOST} "ssh nuc2 'pct exec ${CONTAINER_ID} -- curl -f http://localhost:80/health || echo \"Client not ready yet\"'"
    ssh root@${PROXY_HOST} "ssh nuc2 'pct exec ${CONTAINER_ID} -- curl -f http://localhost:5678/healthz || echo \"n8n not ready yet\"'"
    
    log_success "Deployment completed!"
    
    # Step 10: Display access information
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    DEPLOYMENT COMPLETED                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    echo -e "${YELLOW}📋 Service URLs (Internal):${NC}"
    echo "  • Client Application:   http://*************:80"
    echo "  • n8n Web Interface:    http://*************:5678"
    echo "  • MinIO Console:        http://*************:9001"
    echo "  • Wiki.js:              http://*************:3000"
    echo ""
    echo -e "${YELLOW}🌐 External Access:${NC}"
    echo "  • Domain: robo.stargety.com (needs reverse proxy update)"
    echo "  • Current proxy target: 192.168.0.175"
    echo "  • New proxy target: *************"
    echo ""
    echo -e "${YELLOW}🔑 Default Credentials:${NC}"
    echo "  • n8n:     admin / robo-researcher-2000"
    echo "  • MinIO:   minioadmin / minioadmin"
    echo "  • Wiki.js: <EMAIL> / robo-researcher-2000"
    echo ""
    echo -e "${YELLOW}📚 Next Steps:${NC}"
    echo "  1. Update reverse proxy to point to *************"
    echo "  2. Import n8n workflows from workflows/ directory"
    echo "  3. Configure API credentials in n8n"
    echo "  4. Test the system with sample data"
}

# Container status check
check_container_status() {
    log_info "Checking container 200 status on nuc2..."
    ssh root@${PROXY_HOST} "pvesh get /nodes/nuc2/lxc/200/status/current"
}

# Container resource usage
check_container_resources() {
    log_info "Checking container resource usage..."
    ssh root@${PROXY_HOST} "pvesh get /nodes/nuc2/lxc"
}

# Main execution
case "${1:-deploy}" in
    "status")
        check_container_status
        ;;
    "resources")
        check_container_resources
        ;;
    "deploy")
        deploy_to_container
        ;;
    *)
        echo "Usage: $0 [deploy|status|resources]"
        echo "  deploy    - Deploy ROBO-RESEARCHER-2000 to container 200"
        echo "  status    - Check container status"
        echo "  resources - Check container resource usage"
        exit 1
        ;;
esac
