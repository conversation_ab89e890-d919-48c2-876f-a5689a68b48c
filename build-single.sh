#!/bin/bash

# Build Script for ROBO-RESEARCHER-2000 Single Container
# Creates a self-contained Docker image with all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
IMAGE_NAME="robo-researcher-2000"
IMAGE_TAG="single"
DOCKERFILE="Dockerfile.single"

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check if Dockerfile exists
    if [ ! -f "$DOCKERFILE" ]; then
        log_error "Dockerfile not found: $DOCKERFILE"
        exit 1
    fi
    
    # Check if required directories exist
    local required_dirs=("docker" "workflows" "scripts")
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_warning "Directory not found: $dir (creating empty directory)"
            mkdir -p "$dir"
        fi
    done
    
    log_success "Prerequisites check passed"
}

# Function to create requirements.txt if it doesn't exist
create_requirements() {
    if [ ! -f "requirements.txt" ]; then
        log_info "Creating requirements.txt..."
        cat > requirements.txt <<EOF
# Python dependencies for ROBO-RESEARCHER-2000
requests>=2.28.0
python-dotenv>=0.19.0
psycopg2-binary>=2.9.0
redis>=4.3.0
boto3>=1.26.0
pyyaml>=6.0
EOF
        log_success "requirements.txt created"
    fi
}

# Function to build the Docker image
build_image() {
    log_info "Building Docker image: $IMAGE_NAME:$IMAGE_TAG"
    
    # Build the image
    docker build \
        -f "$DOCKERFILE" \
        -t "$IMAGE_NAME:$IMAGE_TAG" \
        --progress=plain \
        .
    
    if [ $? -eq 0 ]; then
        log_success "Docker image built successfully: $IMAGE_NAME:$IMAGE_TAG"
    else
        log_error "Docker build failed"
        exit 1
    fi
}

# Function to test the image
test_image() {
    log_info "Testing the Docker image..."
    
    # Run a quick test to see if the image starts
    local container_id=$(docker run -d \
        --name robo-test-$$ \
        -e OPENROUTER_API_KEY="test-key" \
        -e SMTP_HOST="test.example.com" \
        -e SMTP_USER="<EMAIL>" \
        -e SMTP_PASSWORD="test-password" \
        "$IMAGE_NAME:$IMAGE_TAG")
    
    if [ $? -eq 0 ]; then
        log_info "Container started, waiting for services..."
        
        # Wait a bit for services to start
        sleep 30
        
        # Check if container is still running
        if docker ps | grep -q "robo-test-$$"; then
            log_success "Container test passed"
            
            # Show container logs
            log_info "Container startup logs:"
            docker logs "robo-test-$$" | tail -20
        else
            log_error "Container stopped unexpectedly"
            docker logs "robo-test-$$"
        fi
        
        # Cleanup test container
        docker stop "robo-test-$$" >/dev/null 2>&1 || true
        docker rm "robo-test-$$" >/dev/null 2>&1 || true
    else
        log_error "Failed to start test container"
        exit 1
    fi
}

# Function to show image information
show_image_info() {
    log_info "Image information:"
    docker images "$IMAGE_NAME:$IMAGE_TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    echo ""
    log_info "To run the container:"
    echo ""
    echo "docker run -d \\"
    echo "  --name robo-researcher \\"
    echo "  -p 5678:5678 \\"
    echo "  -p 9001:9001 \\"
    echo "  -e OPENROUTER_API_KEY=\"your_openrouter_key\" \\"
    echo "  -e SMTP_HOST=\"smtp.gmail.com\" \\"
    echo "  -e SMTP_USER=\"<EMAIL>\" \\"
    echo "  -e SMTP_PASSWORD=\"your_app_password\" \\"
    echo "  -v robo-data:/opt/robo-researcher/data \\"
    echo "  $IMAGE_NAME:$IMAGE_TAG"
    echo ""
    log_info "Access URLs:"
    echo "  • n8n: http://localhost:5678 (admin / robo-researcher-2000)"
    echo "  • MinIO: http://localhost:9001 (minioadmin / minioadmin)"
}

# Function to show usage
show_usage() {
    echo "🤖 ROBO-RESEARCHER-2000 Single Container Build Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --no-test    Skip image testing"
    echo "  --help       Show this help message"
    echo ""
}

# Main function
main() {
    local skip_test=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --no-test)
                skip_test=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    log_info "🤖 Building ROBO-RESEARCHER-2000 Single Container..."
    
    # Run build process
    check_prerequisites
    create_requirements
    build_image
    
    if [ "$skip_test" = false ]; then
        test_image
    else
        log_warning "Skipping image test"
    fi
    
    show_image_info
    
    log_success "🎉 Build completed successfully!"
}

# Run main function
main "$@"
