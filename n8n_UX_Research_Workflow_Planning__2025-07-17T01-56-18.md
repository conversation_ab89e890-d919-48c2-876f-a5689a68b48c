[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[/] NAME:Análisis de Herramientas y Arquitectura DESCRIPTION:Investigar Taguette vs Scripts Python, definir arquitectura del sistema completo (Cliente → n8n → MinIO → Wiki.js → Marp → Excalidraw)
-[x] NAME:Desarrollo del Cliente Web (GitHub Pages) DESCRIPTION:Crear aplicación web estática con formularios para transcripciones, configuración de APIs, y comunicación con n8n via webhooks
-[ ] NAME:inicializar este repositorio, y hacer el primer commit a github. DESCRIPTION:
-[ ] NAME:preparar una carpeta especial para almacenar el workflow de n8n DESCRIPTION:
-[ ] NAME:instalar lo necesario de software selfhosted en nuc1, usar instrucciones, crear nuevos contenedores y nombres asociados al robo-researcher-2000 DESCRIPTION: