---
marp: true
theme: default
class: lead
paginate: true
backgroundColor: #fff
backgroundImage: url('https://marp.app/assets/hero-background.svg')
header: 'ROBO-RESEARCHER-2000 | Investigación UX'
footer: '{{project_name}} | {{date}}'
---

# {{project_name}}
## Resultados de Investigación UX

**Fecha:** {{date}}
**Investigador:** {{researcher_name}}
**Participantes:** {{participant_count}}

---

## 🎯 Objetivos de la Investigación

{{#each objectives}}
- {{this}}
{{/each}}

---

## 📊 Metodología

- **Tipo de estudio:** {{study_type}}
- **Duración promedio:** {{average_duration}} minutos
- **Método de recolección:** {{collection_method}}
- **Herramientas de análisis:** ROBO-RESEARCHER-2000

---

## 🔍 Hallazgos Clave

{{#each key_findings}}
### {{@index}}. {{title}}

> "{{quote}}"
> — {{participant_alias}}

**Frecuencia:** {{frequency}} participantes
**Intensidad:** {{intensity}}/5

---
{{/each}}

## 👥 Arquetipos de Usuarios

{{#each archetypes}}
---

### {{name}}

![{{name}}]({{image_url}})

**Quote representativa:**
> "{{representative_quote}}"

**Jobs to be Done:**
{{#each jobs}}
- {{this}}
{{/each}}

**Fricción crítica:** {{critical_friction}}

**Canal preferido:** {{preferred_channel}}

{{/each}}

---

## 💡 Insights Principales

{{#each insights}}
### Insight {{@index}}

**[{{user_type}}] {{action}} {{context}}**

**Causa raíz:** {{root_cause}}

**Impacto en negocio:** {{business_impact}}

**Prioridad:** {{priority}} (ICE: {{ice_score}})

---
{{/each}}

## 🚀 Oportunidades de Diseño

{{#each hmw_questions}}
### ¿Cómo podríamos...?

**{{question}}**

- **Para que:** {{outcome}}
- **Sin que:** {{constraint}}
- **OKR relacionado:** {{related_okr}}

---
{{/each}}

## 📈 Priorización RICE

| Oportunidad | Reach | Impact | Confidence | Effort | Score |
|-------------|-------|--------|------------|--------|-------|
{{#each opportunities}}
| {{name}} | {{reach}} | {{impact}} | {{confidence}} | {{effort}} | **{{rice_score}}** |
{{/each}}

---

## 🎨 Mapa de Afinidad

![Mapa de Afinidad]({{affinity_map_url}})

**Categorías principales:**
{{#each affinity_categories}}
- **{{name}}:** {{description}} ({{code_count}} códigos)
{{/each}}

---

## 📊 Análisis Cuantitativo

### Distribución de Códigos

{{#each code_frequencies}}
- **{{code_name}}:** {{frequency}} menciones ({{percentage}}%)
{{/each}}

### Intensidad Emocional

- **Momentos de frustración:** {{frustration_intensity}}/5
- **Momentos de satisfacción:** {{satisfaction_intensity}}/5
- **Carga cognitiva promedio:** {{cognitive_load}}/5

---

## ⚠️ Puntos de Dolor Críticos

{{#each pain_points}}
### {{title}}

**Descripción:** {{description}}

**Frecuencia:** {{frequency}} participantes

**Cita representativa:**
> "{{quote}}"
> — {{participant}}

**Impacto:** {{impact_level}}

---
{{/each}}

## ✨ Momentos de Delicia

{{#each delight_moments}}
### {{title}}

**Descripción:** {{description}}

**Cita representativa:**
> "{{quote}}"
> — {{participant}}

**Oportunidad:** {{opportunity}}

---
{{/each}}

## 🔄 Próximos Pasos

### Inmediatos (1-2 semanas)
{{#each immediate_actions}}
- {{this}}
{{/each}}

### Corto plazo (1-3 meses)
{{#each short_term_actions}}
- {{this}}
{{/each}}

### Largo plazo (3-6 meses)
{{#each long_term_actions}}
- {{this}}
{{/each}}

---

## 📋 Recomendaciones

{{#each recommendations}}
### {{priority}} Prioridad: {{title}}

**Descripción:** {{description}}

**Justificación:** {{rationale}}

**Recursos necesarios:** {{resources}}

**Timeline estimado:** {{timeline}}

---
{{/each}}

## 📚 Apéndice

### Metodología Detallada
- **Guión de entrevista:** [Link al documento]
- **Criterios de reclutamiento:** [Link al documento]
- **Proceso de análisis:** ROBO-RESEARCHER-2000

### Datos Adicionales
- **Transcripciones completas:** Disponibles en Wiki.js
- **Códigos detallados:** [Link al repositorio]
- **Datos cuantitativos:** [Link a dashboard]

---

## 🤝 Agradecimientos

**Participantes:** Gracias a todos los usuarios que compartieron su tiempo y experiencias.

**Equipo:** {{team_members}}

**Herramientas:** Análisis automatizado con ROBO-RESEARCHER-2000

---

# ¿Preguntas?

**Contacto:**
- Email: {{researcher_email}}
- Documentación completa: {{wiki_url}}
- Repositorio de datos: {{data_repository_url}}

**Próxima revisión:** {{next_review_date}}
