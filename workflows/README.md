# ROBO-RESEARCHER-2000 Workflows

Esta carpeta contiene todos los workflows de n8n para el sistema de investigación UX automatizado.

## 📋 Workflows Principales

### 1. main-workflow.json
**Workflow principal de 17 pasos** que automatiza todo el proceso de investigación UX:

1. **Webhook Trigger** - Recibe datos del cliente web
2. **Validate Input** - Valida formato y contenido
3. **Upload to MinIO** - Almacena transcripción
4. **Text Preprocessing** - Limpieza y anonimización
5. **Segmentation** - División por tópicos
6. **Deductive Coding** - Códigos predefinidos
7. **Open Coding AI** - IA sugiere códigos emergentes
8. **Category Grouping** - Agrupa códigos similares
9. **Affinity Mapping** - Genera visualizaciones
10. **Quantitative Analysis** - Métricas y frecuencias
11. **Pattern Detection** - IA detecta patrones
12. **Insight Generation** - Genera insights estructurados
13. **Archetype Creation** - Crea arquetipos de usuarios
14. **HMW Generation** - "How Might We" questions
15. **Opportunity Prioritization** - Matriz RICE
16. **Presentation Generation** - Marp → PPTX automático
17. **Documentation & Email** - Wiki.js + notificaciones

### 2. test-workflow.json
Workflow simplificado para testing y desarrollo.

### 3. backup-workflow.json
Workflow para backups automáticos del sistema.

## 🔧 Templates

### Prompts para IA
- `prompts/coding-prompt.txt` - Template para codificación con IA
- `prompts/insight-prompt.txt` - Template para generación de insights
- `prompts/pattern-prompt.txt` - Template para detección de patrones

### Configuraciones
- `config/default-codes.json` - Códigos predefinidos para análisis
- `config/presentation-template.md` - Template Marp para presentaciones
- `config/email-templates/` - Templates de email

## 📥 Importación

### En n8n Web Interface

1. Acceder a http://localhost:5678
2. Login: admin / robo-researcher-2000
3. Ir a "Workflows" → "Import from file"
4. Seleccionar `main-workflow.json`
5. Configurar credenciales necesarias

### Via CLI (si disponible)

```bash
# Importar workflow principal
n8n import:workflow --file=workflows/main-workflow.json

# Importar todos los workflows
for file in workflows/*.json; do
    n8n import:workflow --file="$file"
done
```

## ⚙️ Configuración

### Variables de Entorno Requeridas

```bash
# APIs
OPENROUTER_API_KEY=your_key_here
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
WIKIJS_API_TOKEN=your_token_here

# SMTP
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_password_here

# URLs
N8N_WEBHOOK_URL=http://localhost:5678/webhook/robo-researcher
MINIO_ENDPOINT=http://localhost:9000
WIKIJS_URL=http://localhost:3000
```

### Credenciales en n8n

Configurar las siguientes credenciales en n8n:

1. **OpenRouter API**
   - Tipo: HTTP Request
   - URL: https://openrouter.ai/api/v1
   - Headers: Authorization: Bearer YOUR_API_KEY

2. **MinIO S3**
   - Tipo: S3
   - Endpoint: http://localhost:9000
   - Access Key: minioadmin
   - Secret Key: minioadmin

3. **SMTP**
   - Tipo: SMTP
   - Host: smtp.gmail.com
   - Port: 587
   - User: <EMAIL>
   - Password: your_app_password

4. **Wiki.js API**
   - Tipo: HTTP Request
   - URL: http://localhost:3000/graphql
   - Headers: Authorization: Bearer YOUR_TOKEN

## 🧪 Testing

### Workflow de Prueba

```json
{
  "email": "<EMAIL>",
  "transcription": "Esta es una transcripción de prueba...",
  "project_name": "Test Project",
  "api_keys": {
    "openrouter": "your_key",
    "smtp_password": "your_password"
  }
}
```

### Endpoints de Testing

- **Webhook URL**: `http://localhost:5678/webhook/robo-researcher`
- **Health Check**: `http://localhost:5678/healthz`
- **Metrics**: `http://localhost:5678/metrics`

## 📊 Monitoreo

### Logs de Workflow

```bash
# Ver logs de n8n
docker-compose logs -f robo-researcher-n8n

# Ver execuciones en n8n UI
# http://localhost:5678 → Executions
```

### Métricas

n8n expone métricas Prometheus en `/metrics`:
- Número de ejecuciones
- Tiempo de ejecución
- Errores por nodo
- Estado de workflows

## 🔄 Versionado

### Backup de Workflows

```bash
# Exportar workflow actual
curl -X GET "http://localhost:5678/api/v1/workflows/1/export" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  > workflows/backup/main-workflow-$(date +%Y%m%d).json
```

### Control de Versiones

Los workflows se versionan usando Git:
- `main-workflow-v1.0.json` - Versión inicial
- `main-workflow-v1.1.json` - Mejoras menores
- `main-workflow-v2.0.json` - Cambios mayores

## 🐛 Troubleshooting

### Problemas Comunes

1. **Webhook no responde**
   - Verificar que n8n esté ejecutándose
   - Comprobar URL del webhook
   - Revisar logs de n8n

2. **Error de credenciales**
   - Verificar configuración en n8n UI
   - Comprobar variables de entorno
   - Validar tokens de API

3. **Timeout en ejecución**
   - Aumentar timeout en configuración
   - Optimizar scripts Python
   - Verificar recursos del servidor

### Debug Mode

Habilitar debug en n8n:

```bash
# En docker-compose.yml
environment:
  - N8N_LOG_LEVEL=debug
```

## 📚 Documentación Adicional

- [n8n Documentation](https://docs.n8n.io/)
- [Workflow Best Practices](https://docs.n8n.io/workflows/best-practices/)
- [Custom Nodes Development](https://docs.n8n.io/nodes/creating-nodes/)
